/**
 * WordPress Webpack Configuration
 * 
 * @package PayOp\WooCommerce\Blocks
 */

const defaultConfig = require('@wordpress/scripts/config/webpack.config');
const path = require('path');

module.exports = {
    ...defaultConfig,
    entry: {
        'payment-method': './src/payment-method/index.js',
        'admin-interface': './src/admin/index.js',
    },
    output: {
        path: path.resolve(__dirname, 'build'),
        filename: '[name].js',
    },
    externals: {
        '@woocommerce/blocks-registry': ['wc', 'wcBlocksRegistry'],
        '@woocommerce/settings': ['wc', 'wcSettings'],
        '@wordpress/element': ['wp', 'element'],
        '@wordpress/i18n': ['wp', 'i18n'],
        '@wordpress/components': ['wp', 'components'],
        '@wordpress/data': ['wp', 'data'],
        '@wordpress/api-fetch': ['wp', 'apiFetch'],
        '@wordpress/notices': ['wp', 'notices'],
        '@wordpress/html-entities': ['wp', 'htmlEntities'],
    },
};
