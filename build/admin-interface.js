/**
 * PayOp Admin Interface - Compiled Build
 * This is a placeholder build file until the actual React admin components are compiled
 */

(function() {
    'use strict';
    
    // Check if WordPress components are available
    if (typeof wp === 'undefined' || !wp.element) {
        console.error('PayOp Admin: WordPress components not available');
        return;
    }
    
    const { createElement: e, render } = wp.element;
    const { __ } = wp.i18n;
    
    // Simple Admin Interface Component
    const PayOpAdminInterface = () => {
        return e('div', { className: 'payop-admin-interface' },
            e('div', { className: 'payop-admin-header' },
                e('h1', null, __('PayOp Payment Gateway Management', 'payop')),
                e('p', { className: 'description' }, 
                    __('Manage your PayOp payment groups, methods, and settings.', 'payop')
                )
            ),
            e('div', { className: 'payop-admin-placeholder' },
                e('p', null, __('Admin interface will appear here after React components are built.', 'payop')),
                e('p', null, __('Run "npm run build" to compile the React admin components.', 'payop')),
                e('div', { className: 'payop-admin-links' },
                    e('a', { 
                        href: 'admin.php?page=wc-settings&tab=payop',
                        className: 'button button-primary'
                    }, __('Go to PayOp Settings', 'payop'))
                )
            )
        );
    };
    
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        const adminContainer = document.getElementById('payop-admin-interface');
        
        if (adminContainer) {
            render(e(PayOpAdminInterface), adminContainer);
        }
    });
    
})();
