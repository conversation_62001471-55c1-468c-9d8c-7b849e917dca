/**
 * PayOp Payment Method - Compiled Build
 * This is a placeholder build file until the actual React components are compiled
 */

(function() {
    'use strict';
    
    // Check if WooCommerce Blocks registry is available
    if (typeof wc === 'undefined' || !wc.wcBlocksRegistry) {
        console.error('PayOp: WooCommerce Blocks registry not available');
        return;
    }
    
    const { registerPaymentMethod } = wc.wcBlocksRegistry;
    const { createElement: e } = wp.element;
    const { __ } = wp.i18n;
    const { getSetting } = wc.wcSettings;
    
    // Get PayOp settings
    const settings = getSetting('payop_data', {});
    
    // Simple PayOp Label Component
    const PayOpLabel = ({ label }) => {
        return e('div', { className: 'payop-payment-label' }, 
            e('span', { className: 'payop-label-text' }, label || __('PayOp Payment Methods', 'payop'))
        );
    };
    
    // Simple PayOp Content Component
    const PayOpContent = () => {
        return e('div', { className: 'payop-payment-method' },
            e('p', null, settings.description || __('Pay securely with 122+ payment methods worldwide', 'payop')),
            settings.testMode === 'yes' && e('div', { className: 'payop-test-mode-notice' },
                e('p', null, __('Test mode is enabled. No real payments will be processed.', 'payop'))
            ),
            e('div', { className: 'payop-placeholder' },
                e('p', null, __('Payment method selection will appear here after React components are built.', 'payop')),
                e('p', null, __('Run "npm run build" to compile the React components.', 'payop'))
            )
        );
    };
    
    // PayOp payment method configuration
    const PayOpConfig = {
        name: 'payop',
        label: e(PayOpLabel, { label: settings.title }),
        content: e(PayOpContent),
        edit: e(PayOpContent),
        canMakePayment: () => true,
        supports: {
            features: settings.supports || ['products'],
            showSavedCards: false,
            showSaveOption: false,
        },
        ariaLabel: settings.title || __('PayOp Payment Methods', 'payop'),
    };
    
    // Register the payment method
    registerPaymentMethod(PayOpConfig);
    
})();
