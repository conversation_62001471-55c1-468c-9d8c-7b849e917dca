<?php
/**
 * PayOp Uninstall Script
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Include database class
require_once plugin_dir_path(__FILE__) . 'includes/class-payop-database.php';

/**
 * Clean up PayOp data on uninstall
 */
function payop_uninstall_cleanup(): void {
    // Remove database tables
    PayOp_Database::drop_tables();
    
    // Remove options
    delete_option('woocommerce_payop_blocks_settings');
    delete_option('payop_plugin_version');
    delete_option('payop_db_version');
    
    // Remove transients
    delete_transient('payop_payment_methods');
    delete_transient('payop_payment_groups');
    
    // Clear scheduled events
    wp_clear_scheduled_hook('payop_sync_payment_methods');
    wp_clear_scheduled_hook('payop_cleanup_cache');
    
    // Remove user meta
    delete_metadata('user', 0, 'payop_preferred_payment_group', '', true);
    delete_metadata('user', 0, 'payop_last_used_method', '', true);
    
    // Remove order meta (keep for historical purposes, but clean up test orders)
    global $wpdb;
    
    // Only remove meta from orders in 'trash' status
    $wpdb->query("
        DELETE pm FROM {$wpdb->postmeta} pm
        INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'trash'
        AND pm.meta_key LIKE '_payop_%'
    ");
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

// Run cleanup
payop_uninstall_cleanup();
