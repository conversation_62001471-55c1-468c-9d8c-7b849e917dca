/**
 * PayOp Payment Method Label Component
 * 
 * @package PayOp\WooCommerce\Blocks
 */

import { __ } from '@wordpress/i18n';

/**
 * PayOp Label Component
 */
const PayOpLabel = ({ label, icons }) => {
    return (
        <div className="payop-payment-label">
            <span className="payop-label-text">{label}</span>
            {icons && (
                <div className="payop-payment-icons">
                    {Object.entries(icons).map(([type, iconUrl]) => (
                        <img
                            key={type}
                            src={iconUrl}
                            alt={__(`${type} payments`, 'payop')}
                            className={`payop-icon payop-icon-${type}`}
                            width="24"
                            height="24"
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default PayOpLabel;
