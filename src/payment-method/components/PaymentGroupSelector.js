/**
 * Payment Group Selector Component
 * 
 * @package PayOp\WooCommerce\Blocks
 */

import { __ } from '@wordpress/i18n';

/**
 * Payment Group Selector Component
 */
const PaymentGroupSelector = ({ groups, selectedGroup, onGroupSelect }) => {
    if (!groups || groups.length === 0) {
        return null;
    }
    
    // If only one group, auto-select it and don't show selector
    if (groups.length === 1 && !selectedGroup) {
        onGroupSelect(groups[0]);
        return null;
    }
    
    return (
        <div className="payop-group-selector">
            <h4 className="payop-group-selector-title">
                {__('Choose Payment Type', 'payop')}
            </h4>
            
            <div className="payop-group-tabs">
                {groups.map((group) => (
                    <button
                        key={group.id}
                        className={`payop-group-tab ${
                            selectedGroup?.id === group.id ? 'active' : ''
                        }`}
                        onClick={() => onGroupSelect(group)}
                        style={{
                            borderColor: selectedGroup?.id === group.id ? group.color : 'transparent',
                        }}
                    >
                        <div className="payop-group-tab-content">
                            {group.icon && (
                                <span 
                                    className={`payop-group-icon dashicons dashicons-${group.icon}`}
                                    style={{ color: group.color }}
                                />
                            )}
                            <span className="payop-group-name">{group.name}</span>
                            {group.methodCount > 0 && (
                                <span className="payop-group-count">
                                    {group.methodCount}
                                </span>
                            )}
                        </div>
                        
                        {group.description && (
                            <div className="payop-group-description">
                                {group.description}
                            </div>
                        )}
                    </button>
                ))}
            </div>
        </div>
    );
};

export default PaymentGroupSelector;
