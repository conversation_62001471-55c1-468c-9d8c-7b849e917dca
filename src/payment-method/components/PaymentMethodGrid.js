/**
 * Payment Method Grid Component
 * 
 * @package PayOp\WooCommerce\Blocks
 */

import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { getSetting } from '@woocommerce/settings';

import LoadingSpinner from './LoadingSpinner';

const settings = getSetting('payop_data', {});

/**
 * Payment Method Grid Component
 */
const PaymentMethodGrid = ({ group, selectedMethod, onMethodSelect, cartContext }) => {
    const [methods, setMethods] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    
    /**
     * Load payment methods for the selected group
     */
    useEffect(() => {
        if (!group) return;
        
        loadGroupMethods(group.id);
    }, [group]);
    
    /**
     * Load methods for a specific group
     */
    const loadGroupMethods = async (groupId) => {
        setIsLoading(true);
        setError(null);
        
        try {
            const context = {
                currency: cartContext?.totals?.currency_code,
                country: cartContext?.billingAddress?.country,
                total: cartContext?.totals?.total_price,
            };
            
            const response = await fetch(
                `${settings?.apiEndpoints?.methods}/${groupId}?` + new URLSearchParams(context),
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': settings?.nonce,
                    },
                }
            );
            
            if (!response.ok) {
                throw new Error('Failed to load payment methods');
            }
            
            const data = await response.json();
            setMethods(data.methods || []);
            
        } catch (err) {
            console.error('Error loading payment methods:', err);
            setError(__('Failed to load payment methods for this group.', 'payop'));
        } finally {
            setIsLoading(false);
        }
    };
    
    /**
     * Handle method selection
     */
    const handleMethodClick = (method) => {
        onMethodSelect(method);
    };
    
    /**
     * Render loading state
     */
    if (isLoading) {
        return (
            <div className="payop-method-grid-loading">
                <LoadingSpinner />
                <p>{__('Loading payment methods...', 'payop')}</p>
            </div>
        );
    }
    
    /**
     * Render error state
     */
    if (error) {
        return (
            <div className="payop-method-grid-error">
                <p>{error}</p>
                <button onClick={() => loadGroupMethods(group.id)}>
                    {__('Try Again', 'payop')}
                </button>
            </div>
        );
    }
    
    /**
     * Render no methods available
     */
    if (methods.length === 0) {
        return (
            <div className="payop-method-grid-empty">
                <p>{__('No payment methods available in this group.', 'payop')}</p>
            </div>
        );
    }
    
    /**
     * Main render
     */
    return (
        <div className="payop-method-grid">
            <h5 className="payop-method-grid-title">
                {__('Select Payment Method', 'payop')}
            </h5>
            
            <div className="payop-methods">
                {methods.map((method) => (
                    <div
                        key={method.id}
                        className={`payop-method-card ${
                            selectedMethod?.id === method.id ? 'selected' : ''
                        }`}
                        onClick={() => handleMethodClick(method)}
                    >
                        <div className="payop-method-header">
                            {method.logo && (
                                <img
                                    src={method.logo}
                                    alt={method.title}
                                    className="payop-method-logo"
                                    width="40"
                                    height="40"
                                />
                            )}
                            <div className="payop-method-info">
                                <h6 className="payop-method-title">{method.title}</h6>
                                <span className="payop-method-type">{method.type}</span>
                            </div>
                        </div>
                        
                        {method.description && (
                            <div className="payop-method-description">
                                {method.description}
                            </div>
                        )}
                        
                        {method.currencies && method.currencies.length > 0 && (
                            <div className="payop-method-currencies">
                                <span className="payop-currencies-label">
                                    {__('Currencies:', 'payop')}
                                </span>
                                <span className="payop-currencies-list">
                                    {method.currencies.join(', ')}
                                </span>
                            </div>
                        )}
                        
                        {method.fields && method.fields.length > 0 && (
                            <div className="payop-method-fields-info">
                                <span className="payop-fields-label">
                                    {__('Required fields:', 'payop')} {method.fields.length}
                                </span>
                            </div>
                        )}
                        
                        <div className="payop-method-selection-indicator">
                            {selectedMethod?.id === method.id && (
                                <span className="dashicons dashicons-yes-alt"></span>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default PaymentMethodGrid;
