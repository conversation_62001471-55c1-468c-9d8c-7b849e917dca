/**
 * Error Message Component
 * 
 * @package PayOp\WooCommerce\Blocks
 */

/**
 * Error Message Component
 */
const ErrorMessage = ({ message, type = 'error' }) => {
    return (
        <div className={`payop-message payop-message-${type}`}>
            <span className="payop-message-icon dashicons dashicons-warning"></span>
            <span className="payop-message-text">{message}</span>
        </div>
    );
};

export default ErrorMessage;
