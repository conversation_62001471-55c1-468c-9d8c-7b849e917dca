/**
 * Additional Fields Renderer Component
 * 
 * @package PayOp\WooCommerce\Blocks
 */

import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';

/**
 * Additional Fields Renderer Component
 */
const AdditionalFieldsRenderer = ({ method, billingAddress, onFieldChange }) => {
    const [fieldValues, setFieldValues] = useState({});
    const [fieldErrors, setFieldErrors] = useState({});
    
    /**
     * Get required fields for the selected method
     */
    const requiredFields = method?.fields || [];
    
    /**
     * Filter out standard fields that are already handled by WooCommerce
     */
    const additionalFields = requiredFields.filter(field => 
        !['email', 'name', 'first_name', 'last_name'].includes(field.name)
    );
    
    /**
     * Handle field value change
     */
    const handleFieldChange = (fieldName, value) => {
        setFieldValues(prev => ({
            ...prev,
            [fieldName]: value
        }));
        
        // Clear error when user starts typing
        if (fieldErrors[fieldName]) {
            setFieldErrors(prev => ({
                ...prev,
                [fieldName]: null
            }));
        }
        
        // Notify parent component
        if (onFieldChange) {
            onFieldChange(fieldName, value);
        }
    };
    
    /**
     * Validate field value
     */
    const validateField = (field, value) => {
        const errors = [];
        
        // Check required fields
        if (field.required && (!value || value.trim() === '')) {
            errors.push(__('This field is required', 'payop'));
        }
        
        // Validate based on field type
        if (value && value.trim() !== '') {
            switch (field.type) {
                case 'email':
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                        errors.push(__('Please enter a valid email address', 'payop'));
                    }
                    break;
                    
                case 'phone':
                    if (!/^[+]?[0-9\s\-\(\)]+$/.test(value)) {
                        errors.push(__('Please enter a valid phone number', 'payop'));
                    }
                    break;
                    
                case 'document':
                    if (value.length < 5) {
                        errors.push(__('Document number must be at least 5 characters', 'payop'));
                    }
                    break;
            }
        }
        
        // Custom validation pattern
        if (field.pattern && value && !new RegExp(field.pattern).test(value)) {
            errors.push(field.patternMessage || __('Please enter a valid value', 'payop'));
        }
        
        return errors;
    };
    
    /**
     * Handle field blur (validation)
     */
    const handleFieldBlur = (field) => {
        const value = fieldValues[field.name] || '';
        const errors = validateField(field, value);
        
        setFieldErrors(prev => ({
            ...prev,
            [field.name]: errors.length > 0 ? errors[0] : null
        }));
    };
    
    /**
     * Render field based on type
     */
    const renderField = (field) => {
        const fieldId = `payop-${field.name}`;
        const value = fieldValues[field.name] || '';
        const error = fieldErrors[field.name];
        
        const commonProps = {
            id: fieldId,
            name: `payop/${field.name}`,
            value: value,
            onChange: (e) => handleFieldChange(field.name, e.target.value),
            onBlur: () => handleFieldBlur(field),
            className: `payop-additional-field ${error ? 'has-error' : ''}`,
            required: field.required,
            placeholder: field.placeholder || '',
        };
        
        switch (field.type) {
            case 'select':
                return (
                    <select {...commonProps}>
                        <option value="">
                            {__('Select...', 'payop')}
                        </option>
                        {field.options?.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                );
                
            case 'email':
                return <input {...commonProps} type="email" />;
                
            case 'phone':
                return <input {...commonProps} type="tel" />;
                
            case 'number':
                return <input {...commonProps} type="number" />;
                
            case 'date':
                return <input {...commonProps} type="date" />;
                
            default:
                return <input {...commonProps} type="text" />;
        }
    };
    
    /**
     * Don't render if no additional fields
     */
    if (additionalFields.length === 0) {
        return null;
    }
    
    /**
     * Main render
     */
    return (
        <div className="payop-additional-fields">
            <h5 className="payop-additional-fields-title">
                {__('Additional Information', 'payop')}
            </h5>
            
            <div className="payop-fields-grid">
                {additionalFields.map((field) => (
                    <div key={field.name} className="payop-field-group">
                        <label 
                            htmlFor={`payop-${field.name}`}
                            className="payop-field-label"
                        >
                            {field.title || field.label}
                            {field.required && (
                                <span className="payop-required-indicator">*</span>
                            )}
                        </label>
                        
                        {renderField(field)}
                        
                        {fieldErrors[field.name] && (
                            <div className="payop-field-error">
                                {fieldErrors[field.name]}
                            </div>
                        )}
                        
                        {field.description && (
                            <div className="payop-field-description">
                                {field.description}
                            </div>
                        )}
                    </div>
                ))}
            </div>
            
            <div className="payop-fields-notice">
                <p>
                    {__('Please fill in the required information to complete your payment.', 'payop')}
                </p>
            </div>
        </div>
    );
};

export default AdditionalFieldsRenderer;
