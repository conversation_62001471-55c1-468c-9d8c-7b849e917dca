/**
 * PayOp Payment Method Component
 * 
 * @package PayOp\WooCommerce\Blocks
 */

import { useState, useEffect, useCallback } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { useSelect, useDispatch } from '@wordpress/data';
import { getSetting } from '@woocommerce/settings';

import PaymentGroupSelector from './components/PaymentGroupSelector';
import PaymentMethodGrid from './components/PaymentMethodGrid';
import AdditionalFieldsRenderer from './components/AdditionalFieldsRenderer';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorMessage from './components/ErrorMessage';

// Get PayOp settings
const settings = getSetting('payop_data', {});

/**
 * Main PayOp Payment Method Component
 */
const PayOpPaymentMethod = () => {
    // State management
    const [selectedGroup, setSelectedGroup] = useState(null);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [paymentGroups, setPaymentGroups] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    
    // Get cart and customer data from WooCommerce Store
    const { cart, billingAddress, shippingAddress, isCalculating } = useSelect(select => ({
        cart: select('wc/store/cart').getCartData(),
        billingAddress: select('wc/store/cart').getBillingAddress(),
        shippingAddress: select('wc/store/cart').getShippingAddress(),
        isCalculating: select('wc/store/cart').isCalculating(),
    }));
    
    // Get payment method interface hooks
    const { eventRegistration, emitResponse } = usePaymentMethodInterface();
    const { onPaymentSetup } = eventRegistration;
    
    /**
     * Filter payment groups based on cart context
     */
    const filterPaymentGroups = useCallback(async () => {
        if (!cart || isCalculating) return;
        
        setIsLoading(true);
        setError(null);
        
        try {
            const context = {
                currency: cart.totals?.currency_code,
                country: billingAddress?.country,
                cartTotal: cart.totals?.total_price,
                items: cart.items,
                needsShipping: cart.needs_shipping,
            };
            
            // Filter groups based on context
            const availableGroups = await fetchAvailableGroups(context);
            setPaymentGroups(availableGroups);
            
            // Auto-select first available group if none selected
            if (!selectedGroup && availableGroups.length > 0) {
                setSelectedGroup(availableGroups[0]);
            }
            
        } catch (err) {
            console.error('Error filtering payment groups:', err);
            setError(__('Failed to load payment methods. Please try again.', 'payop'));
        } finally {
            setIsLoading(false);
        }
    }, [cart, billingAddress, isCalculating, selectedGroup]);
    
    /**
     * Handle payment setup for checkout
     */
    useEffect(() => {
        const unsubscribe = onPaymentSetup(async () => {
            if (!selectedMethod) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: __('Please select a payment method', 'payop'),
                };
            }
            
            // Get additional fields data
            const additionalFields = getAdditionalFieldsData();
            
            return {
                type: emitResponse.responseTypes.SUCCESS,
                meta: {
                    paymentMethodData: {
                        selectedGroup: selectedGroup?.id,
                        selectedMethod: selectedMethod?.id,
                        additionalFields: additionalFields,
                    },
                },
            };
        });
        
        return unsubscribe;
    }, [selectedGroup, selectedMethod, onPaymentSetup, emitResponse]);
    
    /**
     * Load payment groups on component mount and cart changes
     */
    useEffect(() => {
        filterPaymentGroups();
    }, [filterPaymentGroups]);
    
    /**
     * Handle group selection
     */
    const handleGroupSelect = useCallback((group) => {
        setSelectedGroup(group);
        setSelectedMethod(null); // Reset method selection when group changes
    }, []);
    
    /**
     * Handle method selection
     */
    const handleMethodSelect = useCallback((method) => {
        setSelectedMethod(method);
    }, []);
    
    /**
     * Render loading state
     */
    if (isLoading) {
        return (
            <div className="payop-payment-method payop-loading">
                <LoadingSpinner />
                <p>{__('Loading payment methods...', 'payop')}</p>
            </div>
        );
    }
    
    /**
     * Render error state
     */
    if (error) {
        return (
            <div className="payop-payment-method payop-error">
                <ErrorMessage message={error} />
                <button 
                    className="payop-retry-button"
                    onClick={filterPaymentGroups}
                >
                    {__('Try Again', 'payop')}
                </button>
            </div>
        );
    }
    
    /**
     * Render no payment methods available
     */
    if (paymentGroups.length === 0) {
        return (
            <div className="payop-payment-method payop-no-methods">
                <p>{__('No payment methods available for your location and currency.', 'payop')}</p>
            </div>
        );
    }
    
    /**
     * Main render
     */
    return (
        <div className="payop-payment-method">
            {/* Payment method description */}
            {settings?.description && (
                <div className="payop-description">
                    <p>{settings.description}</p>
                </div>
            )}
            
            {/* Test mode notice */}
            {settings?.testMode && (
                <div className="payop-test-mode-notice">
                    <p>{__('Test mode is enabled. No real payments will be processed.', 'payop')}</p>
                </div>
            )}
            
            {/* Payment group selector */}
            <PaymentGroupSelector
                groups={paymentGroups}
                selectedGroup={selectedGroup}
                onGroupSelect={handleGroupSelect}
            />
            
            {/* Payment method grid */}
            {selectedGroup && (
                <PaymentMethodGrid
                    group={selectedGroup}
                    selectedMethod={selectedMethod}
                    onMethodSelect={handleMethodSelect}
                    cartContext={cart}
                />
            )}
            
            {/* Additional fields */}
            {selectedMethod && (
                <AdditionalFieldsRenderer
                    method={selectedMethod}
                    billingAddress={billingAddress}
                    onFieldChange={handleAdditionalFieldChange}
                />
            )}
        </div>
    );
};

/**
 * Fetch available payment groups from API
 */
async function fetchAvailableGroups(context) {
    try {
        const response = await fetch(settings?.apiEndpoints?.groups + '?' + new URLSearchParams(context), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': settings?.nonce,
            },
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch payment groups');
        }
        
        const data = await response.json();
        return data.groups || [];
        
    } catch (error) {
        console.error('Error fetching payment groups:', error);
        // Fallback to settings groups if API fails
        return settings?.paymentGroups || [];
    }
}

/**
 * Get additional fields data from form
 */
function getAdditionalFieldsData() {
    const additionalFields = {};
    
    // Get all PayOp additional fields from the form
    const payopFields = document.querySelectorAll('[name^="payop/"]');
    
    payopFields.forEach(field => {
        if (field.value) {
            additionalFields[field.name] = field.value;
        }
    });
    
    return additionalFields;
}

/**
 * Handle additional field changes
 */
function handleAdditionalFieldChange(fieldName, value) {
    // This would typically update form state or trigger validation
    console.log('Additional field changed:', fieldName, value);
}

/**
 * Custom hook for payment method interface
 */
function usePaymentMethodInterface() {
    // This would be provided by WooCommerce Blocks
    // For now, return a mock implementation
    return {
        eventRegistration: {
            onPaymentSetup: (callback) => {
                // Mock implementation
                return () => {};
            },
        },
        emitResponse: {
            responseTypes: {
                SUCCESS: 'success',
                ERROR: 'error',
            },
        },
    };
}

export default PayOpPaymentMethod;
