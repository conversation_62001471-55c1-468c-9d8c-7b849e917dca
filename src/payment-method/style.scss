/**
 * PayOp Payment Method Styles
 * 
 * @package PayOp\WooCommerce\Blocks
 */

.payop-payment-method {
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    
    .payop-description {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 4px;
        
        p {
            margin: 0;
            color: #666;
        }
    }
    
    .payop-test-mode-notice {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        
        p {
            margin: 0;
            color: #856404;
            font-weight: 500;
        }
    }
}

// Payment Group Selector
.payop-group-selector {
    margin-bottom: 1.5rem;
    
    .payop-group-selector-title {
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }
    
    .payop-group-tabs {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
    }
    
    .payop-group-tab {
        padding: 1rem;
        border: 2px solid transparent;
        border-radius: 8px;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
        
        &:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        &.active {
            background: #fff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .payop-group-tab-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .payop-group-icon {
            font-size: 1.2rem;
        }
        
        .payop-group-name {
            font-weight: 600;
            flex: 1;
        }
        
        .payop-group-count {
            background: #6c757d;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .payop-group-description {
            font-size: 0.875rem;
            color: #666;
            margin: 0;
        }
    }
}

// Payment Method Grid
.payop-method-grid {
    margin-bottom: 1.5rem;
    
    .payop-method-grid-title {
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }
    
    .payop-methods {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
    }
    
    .payop-method-card {
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        
        &:hover {
            border-color: #007cba;
            box-shadow: 0 2px 8px rgba(0, 124, 186, 0.1);
        }
        
        &.selected {
            border-color: #007cba;
            background: #f0f8ff;
            box-shadow: 0 4px 12px rgba(0, 124, 186, 0.2);
        }
        
        .payop-method-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }
        
        .payop-method-logo {
            border-radius: 4px;
            object-fit: contain;
        }
        
        .payop-method-info {
            flex: 1;
        }
        
        .payop-method-title {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }
        
        .payop-method-type {
            font-size: 0.875rem;
            color: #666;
            text-transform: capitalize;
        }
        
        .payop-method-description {
            margin-bottom: 0.75rem;
            font-size: 0.875rem;
            color: #666;
        }
        
        .payop-method-currencies,
        .payop-method-fields-info {
            margin-bottom: 0.5rem;
            font-size: 0.75rem;
            
            .payop-currencies-label,
            .payop-fields-label {
                font-weight: 600;
                color: #333;
            }
            
            .payop-currencies-list {
                color: #666;
            }
        }
        
        .payop-method-selection-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            
            .dashicons {
                color: #007cba;
                font-size: 1.2rem;
            }
        }
    }
}

// Additional Fields
.payop-additional-fields {
    .payop-additional-fields-title {
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }
    
    .payop-fields-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .payop-field-group {
        .payop-field-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
            
            .payop-required-indicator {
                color: #dc3545;
                margin-left: 0.25rem;
            }
        }
        
        .payop-additional-field {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            
            &:focus {
                outline: none;
                border-color: #007cba;
                box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
            }
            
            &.has-error {
                border-color: #dc3545;
                
                &:focus {
                    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
                }
            }
        }
        
        .payop-field-error {
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #dc3545;
        }
        
        .payop-field-description {
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #666;
        }
    }
    
    .payop-fields-notice {
        padding: 0.75rem;
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        
        p {
            margin: 0;
            font-size: 0.875rem;
            color: #0066cc;
        }
    }
}

// Loading and Error States
.payop-loading,
.payop-error,
.payop-no-methods {
    text-align: center;
    padding: 2rem;
    
    p {
        margin: 0.5rem 0 0 0;
        color: #666;
    }
}

.payop-retry-button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
        background: #005a87;
    }
}

// Loading Spinner
.payop-loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    
    &.payop-spinner-small {
        .payop-spinner {
            width: 20px;
            height: 20px;
        }
    }
    
    &.payop-spinner-medium {
        .payop-spinner {
            width: 30px;
            height: 30px;
        }
    }
    
    &.payop-spinner-large {
        .payop-spinner {
            width: 40px;
            height: 40px;
        }
    }
}

.payop-spinner {
    display: flex;
    gap: 2px;
    
    .payop-spinner-circle {
        width: 6px;
        height: 6px;
        background: #007cba;
        border-radius: 50%;
        animation: payop-bounce 1.4s ease-in-out infinite both;
        
        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
    }
}

@keyframes payop-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

// Messages
.payop-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 4px;
    
    &.payop-message-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    &.payop-message-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
    
    &.payop-message-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .payop-message-icon {
        font-size: 1.1rem;
    }
    
    .payop-message-text {
        flex: 1;
    }
}
