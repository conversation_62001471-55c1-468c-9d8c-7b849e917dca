/**
 * PayOp Payment Method Registration
 * 
 * @package PayOp\WooCommerce\Blocks
 */

import { registerPaymentMethod } from '@woocommerce/blocks-registry';
import { __ } from '@wordpress/i18n';
import { getSetting } from '@woocommerce/settings';
import { decodeEntities } from '@wordpress/html-entities';

import PayOpPaymentMethod from './payment-method';
import PayOpLabel from './components/PayOpLabel';

// Get PayOp settings from server
const settings = getSetting('payop_data', {});

const defaultLabel = __('PayOp Payment Methods', 'payop');
const label = decodeEntities(settings?.title) || defaultLabel;

/**
 * PayOp payment method configuration
 */
const PayOpConfig = {
    name: 'payop',
    label: <PayOpLabel label={label} icons={settings?.icons} />,
    content: <PayOpPaymentMethod />,
    edit: <PayOpPaymentMethod />,
    canMakePayment: ({ cart, cartTotals, billingAddress, shippingAddress }) => {
        // Check if PayOp is available for current cart context
        return validatePaymentAvailability({
            cart,
            cartTotals,
            billingAddress,
            shippingAddress,
            settings
        });
    },
    supports: {
        features: settings?.supports || ['products'],
        showSavedCards: false,
        showSaveOption: false,
    },
    ariaLabel: label,
};

/**
 * Validate if PayOp payment is available for current context
 */
function validatePaymentAvailability({ cart, cartTotals, billingAddress, settings }) {
    // Check if payment groups are available
    if (!settings?.paymentGroups || settings.paymentGroups.length === 0) {
        return false;
    }
    
    // Check minimum amount if configured
    if (settings?.minAmount && cartTotals?.total_price < settings.minAmount) {
        return false;
    }
    
    // Check maximum amount if configured
    if (settings?.maxAmount && cartTotals?.total_price > settings.maxAmount) {
        return false;
    }
    
    // Check currency support
    const currency = cartTotals?.currency_code;
    if (currency && settings?.supportedCurrencies) {
        if (!settings.supportedCurrencies.includes(currency)) {
            return false;
        }
    }
    
    // Check country support
    const country = billingAddress?.country;
    if (country && settings?.supportedCountries) {
        if (!settings.supportedCountries.includes(country)) {
            return false;
        }
    }
    
    return true;
}

// Register the payment method
registerPaymentMethod(PayOpConfig);
