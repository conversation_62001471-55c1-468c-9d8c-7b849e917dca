/**
 * PayOp Admin Interface Component
 * 
 * @package PayOp\WooCommerce\Blocks\Admin
 */

import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { 
    Button, 
    Card, 
    CardBody, 
    TabPanel,
    Notice,
    Spinner
} from '@wordpress/components';

import PayOpGroupBuilder from './components/PayOpGroupBuilder';
import PayOpMethodManager from './components/PayOpMethodManager';
import PayOpSettings from './components/PayOpSettings';

/**
 * Main Admin Interface Component
 */
const PayOpAdminInterface = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [notices, setNotices] = useState([]);
    const [activeTab, setActiveTab] = useState('groups');
    
    // Get admin data from localized script
    const adminData = window.payopAdmin || {};
    
    /**
     * Add notice
     */
    const addNotice = (message, type = 'success') => {
        const notice = {
            id: Date.now(),
            message,
            type,
        };
        
        setNotices(prev => [...prev, notice]);
        
        // Auto-remove notice after 5 seconds
        setTimeout(() => {
            removeNotice(notice.id);
        }, 5000);
    };
    
    /**
     * Remove notice
     */
    const removeNotice = (noticeId) => {
        setNotices(prev => prev.filter(notice => notice.id !== noticeId));
    };
    
    /**
     * Tab configuration
     */
    const tabs = [
        {
            name: 'groups',
            title: __('Payment Groups', 'payop'),
            content: (
                <PayOpGroupBuilder
                    adminData={adminData}
                    onNotice={addNotice}
                    setLoading={setIsLoading}
                />
            ),
        },
        {
            name: 'methods',
            title: __('Payment Methods', 'payop'),
            content: (
                <PayOpMethodManager
                    adminData={adminData}
                    onNotice={addNotice}
                    setLoading={setIsLoading}
                />
            ),
        },
        {
            name: 'settings',
            title: __('Settings', 'payop'),
            content: (
                <PayOpSettings
                    adminData={adminData}
                    onNotice={addNotice}
                    setLoading={setIsLoading}
                />
            ),
        },
    ];
    
    /**
     * Render notices
     */
    const renderNotices = () => {
        if (notices.length === 0) return null;
        
        return (
            <div className="payop-admin-notices">
                {notices.map(notice => (
                    <Notice
                        key={notice.id}
                        status={notice.type}
                        onRemove={() => removeNotice(notice.id)}
                        isDismissible
                    >
                        {notice.message}
                    </Notice>
                ))}
            </div>
        );
    };
    
    /**
     * Main render
     */
    return (
        <div className="payop-admin-interface">
            {/* Header */}
            <div className="payop-admin-header">
                <h1>{__('PayOp Payment Gateway Management', 'payop')}</h1>
                <p className="description">
                    {__('Manage your PayOp payment groups, methods, and settings.', 'payop')}
                </p>
            </div>
            
            {/* Loading overlay */}
            {isLoading && (
                <div className="payop-admin-loading-overlay">
                    <Spinner />
                    <p>{__('Loading...', 'payop')}</p>
                </div>
            )}
            
            {/* Notices */}
            {renderNotices()}
            
            {/* Main content */}
            <Card>
                <CardBody>
                    <TabPanel
                        className="payop-admin-tabs"
                        activeClass="is-active"
                        tabs={tabs}
                        onSelect={(tabName) => setActiveTab(tabName)}
                    >
                        {(tab) => (
                            <div className="payop-admin-tab-content">
                                {tab.content}
                            </div>
                        )}
                    </TabPanel>
                </CardBody>
            </Card>
            
            {/* Footer */}
            <div className="payop-admin-footer">
                <p>
                    {__('PayOp WooCommerce Blocks Payment Gateway', 'payop')} - 
                    <a href="https://payop.com" target="_blank" rel="noopener noreferrer">
                        {__('Documentation', 'payop')}
                    </a>
                </p>
            </div>
        </div>
    );
};

export default PayOpAdminInterface;
