/**
 * PayOp Admin Interface Entry Point
 * 
 * @package PayOp\WooCommerce\Blocks\Admin
 */

import { render } from '@wordpress/element';
import { __ } from '@wordpress/i18n';

import PayOpAdminInterface from './PayOpAdminInterface';

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
    const adminContainer = document.getElementById('payop-admin-interface');
    
    if (adminContainer) {
        render(<PayOpAdminInterface />, adminContainer);
    }
});
