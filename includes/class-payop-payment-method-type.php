<?php
/**
 * PayOp Payment Method Type for WooCommerce Blocks
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only proceed if WooCommerce Blocks is available
if (class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {

/**
 * PayOp Payment Method Type Class
 */
final class PayOp_Payment_Method_Type extends AbstractPaymentMethodType {
    
    /**
     * Payment method name
     */
    protected $name = 'payop';
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Initialize payment method
     */
    public function initialize(): void {
        $this->settings = get_option('woocommerce_payop_blocks_settings', []);
        
        // Enforce WooCommerce 8.3+ requirement
        if (version_compare(WC_VERSION, '8.3.0', '<')) {
            throw new \Exception('PayOp requires WooCommerce 8.3+ for blocks-based checkout');
        }
        
        // Register hooks
        add_action('woocommerce_rest_checkout_process_payment_with_context', [$this, 'process_payment_with_context'], 10, 2);
    }
    
    /**
     * Check if payment method is active
     */
    public function is_active(): bool {
        return filter_var($this->get_setting('enabled', false), FILTER_VALIDATE_BOOLEAN) 
            && $this->is_blocks_checkout_available()
            && $this->has_required_settings();
    }
    
    /**
     * Get payment method script handles
     */
    public function get_payment_method_script_handles(): array {
        wp_register_script(
            'payop-blocks-payment-method',
            PAYOP_PLUGIN_URL . 'build/payment-method.js',
            [
                'wc-blocks-registry',
                'wc-settings',
                'wp-element',
                'wp-html-entities',
                'wp-i18n',
                'wp-data',
            ],
            PAYOP_VERSION,
            true
        );
        
        wp_set_script_translations(
            'payop-blocks-payment-method',
            'payop',
            PAYOP_PLUGIN_DIR . 'languages'
        );
        
        return ['payop-blocks-payment-method'];
    }
    
    /**
     * Get payment method data for frontend
     */
    public function get_payment_method_data(): array {
        return [
            'title' => $this->get_setting('title', __('PayOp Payment Methods', 'payop')),
            'description' => $this->get_setting('description', __('Pay securely with 122+ payment methods worldwide', 'payop')),
            'supports' => ['products'],
            'paymentGroups' => $this->get_payment_groups_for_blocks(),
            'additionalFields' => $this->get_additional_fields_config(),
            'apiEndpoints' => $this->get_store_api_endpoints(),
            'testMode' => $this->get_setting('test_mode', 'yes') === 'yes',
            'icons' => $this->get_payment_method_icons(),
        ];
    }
    
    /**
     * Process payment with context
     */
    public function process_payment_with_context(\WP_REST_Request $request, $context) {
        try {
            $order = $context->order;
            $payment_data = $request->get_param('payment_data') ?? [];
            
            // Get selected payment method and group
            $selected_method = $payment_data['selectedMethod'] ?? null;
            $selected_group = $payment_data['selectedGroup'] ?? null;
            $additional_fields = $payment_data['additionalFields'] ?? [];
            
            if (!$selected_method) {
                throw new \Exception(__('Please select a payment method', 'payop'));
            }
            
            // Create PayOp invoice
            $api_client = new PayOp_API_Client($request);
            $invoice_id = $api_client->create_invoice_from_store_api(
                $this->prepare_order_data($order),
                $context->get_context_data()
            );
            
            // Create checkout with additional fields
            $checkout_data = $api_client->create_checkout_for_blocks(
                $invoice_id,
                $this->prepare_customer_data($order),
                $additional_fields
            );
            
            // Store payment data in order meta
            $order->update_meta_data('_payop_invoice_id', $invoice_id);
            $order->update_meta_data('_payop_selected_method', $selected_method);
            $order->update_meta_data('_payop_selected_group', $selected_group);
            $order->update_meta_data('_payop_checkout_data', $checkout_data);
            $order->save();
            
            // Return redirect result
            return [
                'result' => 'success',
                'redirect' => $checkout_data['redirectUrl'] ?? '',
                'payment_data' => [
                    'payop_invoice_id' => $invoice_id,
                    'redirect_url' => $checkout_data['redirectUrl'] ?? '',
                ]
            ];

        } catch (\Exception $e) {
            if (function_exists('wc_get_logger')) {
                wc_get_logger()->error(
                    'PayOp payment processing error: ' . $e->getMessage(),
                    ['source' => 'payop']
                );
            }

            return [
                'result' => 'failure',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if blocks checkout is available
     */
    private function is_blocks_checkout_available(): bool {
        return function_exists('woocommerce_register_additional_checkout_field');
    }
    
    /**
     * Check if required settings are configured
     */
    private function has_required_settings(): bool {
        $required_settings = ['public_key', 'secret_key', 'jwt_token'];
        
        foreach ($required_settings as $setting) {
            if (empty($this->get_setting($setting))) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get payment groups for blocks
     */
    private function get_payment_groups_for_blocks(): array {
        return PayOp_Payment_Groups::instance()->get_groups_for_blocks();
    }
    
    /**
     * Get additional fields configuration
     */
    private function get_additional_fields_config(): array {
        return PayOp_Additional_Fields::instance()->get_fields_config_for_blocks();
    }
    
    /**
     * Get Store API endpoints
     */
    private function get_store_api_endpoints(): array {
        return [
            'groups' => rest_url('payop/v1/groups'),
            'methods' => rest_url('payop/v1/methods'),
            'checkout' => rest_url('payop/v1/checkout'),
        ];
    }
    
    /**
     * Get payment method icons
     */
    private function get_payment_method_icons(): array {
        return [
            'cards' => PAYOP_PLUGIN_URL . 'assets/images/cards.svg',
            'banking' => PAYOP_PLUGIN_URL . 'assets/images/banking.svg',
            'wallets' => PAYOP_PLUGIN_URL . 'assets/images/wallets.svg',
            'cash' => PAYOP_PLUGIN_URL . 'assets/images/cash.svg',
            'crypto' => PAYOP_PLUGIN_URL . 'assets/images/crypto.svg',
        ];
    }
    
    /**
     * Prepare order data for PayOp API
     */
    private function prepare_order_data(\WC_Order $order): array {
        return [
            'id' => $order->get_id(),
            'amount' => $order->get_total(),
            'currency' => $order->get_currency(),
            'description' => sprintf(__('Order #%s', 'payop'), $order->get_order_number()),
            'customer_email' => $order->get_billing_email(),
            'customer_name' => $order->get_formatted_billing_full_name(),
            'return_url' => $order->get_checkout_order_received_url(),
            'cancel_url' => function_exists('wc_get_checkout_url') ? wc_get_checkout_url() : home_url(),
        ];
    }
    
    /**
     * Prepare customer data for PayOp API
     */
    private function prepare_customer_data(\WC_Order $order): array {
        return [
            'email' => $order->get_billing_email(),
            'name' => $order->get_formatted_billing_full_name(),
            'phone' => $order->get_billing_phone(),
            'country' => $order->get_billing_country(),
            'city' => $order->get_billing_city(),
            'address' => $order->get_billing_address_1(),
        ];
    }
}

} // End of conditional class loading

else {
    /**
     * Stub class when WooCommerce Blocks is not available
     */
    final class PayOp_Payment_Method_Type {
        private static ?self $instance = null;

        public static function instance(): self {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }

        private function __construct() {
            // Stub constructor
        }

        public function initialize(): void {
            // Stub method
        }
    }
}
