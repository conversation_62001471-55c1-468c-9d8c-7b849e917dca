<?php
/**
 * PayOp Payment Groups Management
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Payment Groups Class
 */
final class PayOp_Payment_Groups {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize hooks if needed
    }
    
    /**
     * Get all configured payment groups formatted for React components
     */
    public function get_groups_for_blocks(): array {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_groups';
        
        $groups = $wpdb->get_results(
            "SELECT * FROM $table WHERE is_enabled = 1 ORDER BY display_order ASC",
            ARRAY_A
        );
        
        if (!$groups) {
            return [];
        }
        
        return array_map([$this, 'format_group_for_react'], $groups);
    }
    
    /**
     * Get groups available for current cart context (blocks-aware)
     */
    public function get_available_groups_for_blocks(array $cart_context): array {
        $all_groups = $this->get_groups_for_blocks();
        $available_groups = [];
        
        foreach ($all_groups as $group) {
            if ($this->is_group_available_for_context($group, $cart_context)) {
                $available_groups[] = $group;
            }
        }
        
        return $available_groups;
    }
    
    /**
     * Create new payment group with React admin interface data
     */
    public function create_group_from_react(array $react_group_data): int {
        global $wpdb;
        
        $group_data = $this->sanitize_react_group_data($react_group_data);
        $group_data['created_at'] = current_time('mysql');
        $group_data['updated_at'] = current_time('mysql');
        
        $table = $wpdb->prefix . 'payop_payment_groups';
        
        $result = $wpdb->insert($table, $group_data);
        
        if ($result === false) {
            throw new \Exception(__('Failed to create payment group', 'payop'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Update group via React admin interface
     */
    public function update_group_from_react(int $group_id, array $react_group_data): bool {
        global $wpdb;
        
        $group_data = $this->sanitize_react_group_data($react_group_data);
        $group_data['updated_at'] = current_time('mysql');
        
        $table = $wpdb->prefix . 'payop_payment_groups';
        
        $result = $wpdb->update(
            $table,
            $group_data,
            ['id' => $group_id],
            null,
            ['%d']
        );
        
        return $result !== false;
    }
    
    /**
     * Delete payment group
     */
    public function delete_group(int $group_id): bool {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_groups';
        
        $result = $wpdb->delete($table, ['id' => $group_id], ['%d']);
        
        return $result !== false;
    }
    
    /**
     * Get payment methods for specific group (React-optimized)
     */
    public function get_group_methods_for_blocks(int $group_id, array $cart_context = []): array {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_group_methods';
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results($wpdb->prepare(
            "SELECT m.*, gm.display_order as group_display_order, gm.blocks_config
             FROM $methods_table m
             INNER JOIN $groups_table gm ON m.method_id = gm.method_id
             WHERE gm.group_id = %d AND gm.is_enabled = 1 AND m.is_enabled = 1
             ORDER BY gm.display_order ASC",
            $group_id
        ), ARRAY_A);
        
        if (!$methods) {
            return [];
        }
        
        return array_map([$this, 'format_method_for_react_display'], $methods);
    }
    
    /**
     * Assign payment methods to group
     */
    public function assign_methods_to_group(int $group_id, array $method_ids): bool {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_group_methods';
        
        // First, remove existing assignments
        $wpdb->delete($table, ['group_id' => $group_id], ['%d']);
        
        // Then add new assignments
        foreach ($method_ids as $index => $method_id) {
            $result = $wpdb->insert($table, [
                'group_id' => $group_id,
                'method_id' => $method_id,
                'display_order' => $index,
                'is_enabled' => 1,
            ]);
            
            if ($result === false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Real-time group filtering for React components
     */
    public function filter_groups_realtime(array $cart_data, array $customer_data): array {
        $context = array_merge($cart_data, $customer_data);
        return $this->get_available_groups_for_blocks($context);
    }
    
    /**
     * Format group data for React component consumption
     */
    private function format_group_for_react(array $group): array {
        $react_config = !empty($group['react_config']) ? json_decode($group['react_config'], true) : [];
        $conditions = !empty($group['conditions']) ? json_decode($group['conditions'], true) : [];
        
        return [
            'id' => (int) $group['id'],
            'name' => $group['name'],
            'description' => $group['description'] ?? '',
            'displayOrder' => (int) $group['display_order'],
            'isEnabled' => (bool) $group['is_enabled'],
            'icon' => $react_config['icon'] ?? 'payment',
            'color' => $react_config['color'] ?? '#6b7280',
            'conditions' => $conditions,
            'methodCount' => $this->get_group_method_count((int) $group['id']),
        ];
    }
    
    /**
     * Sanitize data received from React admin interface
     */
    private function sanitize_react_group_data(array $react_data): array {
        return [
            'name' => sanitize_text_field($react_data['name'] ?? ''),
            'description' => sanitize_textarea_field($react_data['description'] ?? ''),
            'display_order' => (int) ($react_data['displayOrder'] ?? 0),
            'is_enabled' => (bool) ($react_data['isEnabled'] ?? true),
            'conditions' => wp_json_encode($react_data['conditions'] ?? []),
            'react_config' => wp_json_encode([
                'icon' => sanitize_text_field($react_data['icon'] ?? 'payment'),
                'color' => sanitize_hex_color($react_data['color'] ?? '#6b7280'),
            ]),
        ];
    }
    
    /**
     * Check if group is available for given context
     */
    private function is_group_available_for_context(array $group, array $context): bool {
        $conditions = $group['conditions'] ?? [];
        
        // Check currency conditions
        if (!empty($conditions['currencies']) && !empty($context['currency'])) {
            if (!in_array($context['currency'], $conditions['currencies'])) {
                return false;
            }
        }
        
        // Check country conditions
        if (!empty($conditions['countries']) && !empty($context['country'])) {
            if (!in_array($context['country'], $conditions['countries'])) {
                return false;
            }
        }
        
        // Check amount conditions
        if (!empty($context['total'])) {
            $total = (float) $context['total'];
            
            if (!empty($conditions['min_amount']) && $total < (float) $conditions['min_amount']) {
                return false;
            }
            
            if (!empty($conditions['max_amount']) && $total > (float) $conditions['max_amount']) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Format method for React display
     */
    private function format_method_for_react_display(array $method): array {
        $config_fields = !empty($method['config_fields']) ? json_decode($method['config_fields'], true) : [];
        $blocks_config = !empty($method['blocks_config']) ? json_decode($method['blocks_config'], true) : [];
        
        return [
            'id' => (int) $method['method_id'],
            'title' => $method['method_title'],
            'type' => $method['method_type'],
            'logo' => $method['method_logo_url'] ?? '',
            'currencies' => json_decode($method['currencies'], true) ?? [],
            'countries' => json_decode($method['countries'], true) ?? [],
            'fields' => $config_fields['fields'] ?? [],
            'displayOrder' => (int) $method['group_display_order'],
            'blocksConfig' => $blocks_config,
        ];
    }
    
    /**
     * Get method count for group
     */
    private function get_group_method_count(int $group_id): int {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_group_methods';
        
        return (int) $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE group_id = %d AND is_enabled = 1",
            $group_id
        ));
    }
}
