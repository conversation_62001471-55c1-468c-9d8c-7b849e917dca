<?php
/**
 * PayOp Additional Checkout Fields Integration
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Additional Fields Class
 */
final class PayOp_Additional_Fields {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Registered fields cache
     */
    private array $registered_fields = [];
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks(): void {
        add_action('woocommerce_init', [$this, 'register_dynamic_fields']);
        add_action('woocommerce_store_api_checkout_update_order_from_request', [$this, 'save_additional_fields_to_order'], 10, 2);
    }
    
    /**
     * Register dynamic PayOp fields
     */
    public function register_dynamic_fields(): void {
        // Only register if blocks checkout is available
        if (!function_exists('woocommerce_register_additional_checkout_field')) {
            return;
        }

        $payment_methods = PayOp_Payment_Methods::get_all_methods_for_blocks();

        foreach ($payment_methods as $method) {
            $this->register_method_fields($method);
        }
    }
    
    /**
     * Get fields configuration for blocks
     */
    public function get_fields_config_for_blocks(): array {
        return $this->registered_fields;
    }
    
    /**
     * Register fields for a specific payment method
     */
    private function register_method_fields(array $method): void {
        $required_fields = $method['config']['fields'] ?? [];
        
        foreach ($required_fields as $field) {
            // Skip standard WooCommerce fields
            if (in_array($field['name'], ['email', 'name', 'first_name', 'last_name'])) {
                continue;
            }
            
            $field_id = "payop/{$field['name']}";
            
            woocommerce_register_additional_checkout_field([
                'id' => $field_id,
                'label' => $field['title'] ?? $this->format_field_label($field['name']),
                'location' => 'order',
                'type' => $this->map_field_type($field['type']),
                'required' => $this->get_conditional_required_schema($field, $method),
                'hidden' => $this->get_conditional_hidden_schema($field, $method),
                'validation' => $this->get_field_validation_schema($field),
                'options' => $this->get_field_options($field),
                'attributes' => $this->get_field_attributes($field),
                'sanitize_callback' => [$this, 'sanitize_field_value'],
                'validate_callback' => [$this, 'validate_field_value'],
            ]);
            
            // Store field configuration for React components
            $this->registered_fields[$field_id] = [
                'id' => $field_id,
                'name' => $field['name'],
                'label' => $field['title'] ?? $this->format_field_label($field['name']),
                'type' => $this->map_field_type($field['type']),
                'required' => $field['required'] ?? false,
                'methodId' => $method['identifier'],
                'validation' => $this->get_field_validation_schema($field),
                'options' => $this->get_field_options($field),
            ];
        }
    }
    
    /**
     * Get conditional required schema
     */
    private function get_conditional_required_schema(array $field, array $method): array {
        return [
            'type' => 'object',
            'properties' => [
                'checkout' => [
                    'properties' => [
                        'payment_method' => ['const' => 'payop'],
                        'additional_fields' => [
                            'properties' => [
                                'payop/selected_method' => ['const' => (string)$method['identifier']]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Get conditional hidden schema
     */
    private function get_conditional_hidden_schema(array $field, array $method): array {
        return [
            'type' => 'object',
            'properties' => [
                'checkout' => [
                    'properties' => [
                        'payment_method' => ['not' => ['const' => 'payop']],
                        'additional_fields' => [
                            'properties' => [
                                'payop/selected_method' => ['not' => ['const' => (string)$method['identifier']]]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Get field validation schema
     */
    private function get_field_validation_schema(array $field): array {
        $schema = ['type' => 'string'];
        
        if (isset($field['regexp'])) {
            $schema['pattern'] = $field['regexp'];
            $schema['errorMessage'] = sprintf(
                __('Please enter a valid %s', 'payop'),
                $field['title'] ?? $field['name']
            );
        }
        
        // Add specific validation for common field types
        switch ($field['type']) {
            case 'email':
                $schema['format'] = 'email';
                break;
            case 'phone':
                $schema['pattern'] = '^[+]?[0-9\s\-\(\)]+$';
                break;
            case 'document':
                $schema['minLength'] = 5;
                $schema['maxLength'] = 20;
                break;
        }
        
        return $schema;
    }
    
    /**
     * Get field options for select fields
     */
    private function get_field_options(array $field): array {
        if ($field['type'] !== 'select' && $field['type'] !== 'bank_code') {
            return [];
        }
        
        $options = [];
        
        if (isset($field['options'])) {
            foreach ($field['options'] as $value => $label) {
                $options[] = [
                    'value' => $value,
                    'label' => $label,
                ];
            }
        }
        
        return $options;
    }
    
    /**
     * Get field attributes
     */
    private function get_field_attributes(array $field): array {
        $attributes = [];
        
        if (isset($field['placeholder'])) {
            $attributes['placeholder'] = $field['placeholder'];
        }
        
        if (isset($field['maxlength'])) {
            $attributes['maxlength'] = $field['maxlength'];
        }
        
        if (isset($field['pattern'])) {
            $attributes['pattern'] = $field['pattern'];
        }
        
        return $attributes;
    }
    
    /**
     * Map PayOp field types to WooCommerce field types
     */
    private function map_field_type(string $payop_type): string {
        $type_mapping = [
            'text' => 'text',
            'email' => 'text', // WooCommerce blocks only supports text, select, checkbox
            'phone' => 'text', // Changed from 'tel' to 'text' - WooCommerce blocks doesn't support 'tel'
            'number' => 'text', // Changed from 'number' to 'text'
            'select' => 'select',
            'bank_code' => 'select',
            'document' => 'text',
            'date' => 'text', // Changed from 'date' to 'text'
        ];
        
        return $type_mapping[$payop_type] ?? 'text';
    }
    
    /**
     * Format field label
     */
    private function format_field_label(string $field_name): string {
        $labels = [
            'phone' => __('Phone Number', 'payop'),
            'document' => __('Document Number', 'payop'),
            'bank_code' => __('Bank Code', 'payop'),
            'account_number' => __('Account Number', 'payop'),
            'routing_number' => __('Routing Number', 'payop'),
            'swift_code' => __('SWIFT Code', 'payop'),
            'iban' => __('IBAN', 'payop'),
        ];
        
        return $labels[$field_name] ?? ucwords(str_replace('_', ' ', $field_name));
    }
    
    /**
     * Sanitize field value
     */
    public function sanitize_field_value($value, \WP_REST_Request $request, string $param): string {
        return sanitize_text_field($value);
    }
    
    /**
     * Validate field value
     */
    public function validate_field_value($value, \WP_REST_Request $request, string $param): bool|\WP_Error {
        // Get field configuration
        $field_config = $this->registered_fields[$param] ?? null;
        
        if (!$field_config) {
            return true;
        }
        
        // Check if field is required
        if ($field_config['required'] && empty($value)) {
            return new \WP_Error(
                'required_field',
                sprintf(__('%s is required', 'payop'), $field_config['label'])
            );
        }
        
        // Validate based on field type
        switch ($field_config['type']) {
            case 'email':
                if (!empty($value) && !is_email($value)) {
                    return new \WP_Error(
                        'invalid_email',
                        __('Please enter a valid email address', 'payop')
                    );
                }
                break;
                
            case 'phone': // Changed from 'tel' to 'phone' to match our field naming
                if (!empty($value) && !preg_match('/^[+]?[0-9\s\-\(\)]+$/', $value)) {
                    return new \WP_Error(
                        'invalid_phone',
                        __('Please enter a valid phone number', 'payop')
                    );
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Save additional fields to order
     */
    public function save_additional_fields_to_order(\WC_Order $order, \WP_REST_Request $request): void {
        $additional_fields = $request->get_param('additional_fields') ?? [];
        
        foreach ($additional_fields as $field_id => $value) {
            if (strpos($field_id, 'payop/') === 0) {
                $order->update_meta_data('_' . str_replace('/', '_', $field_id), $value);
            }
        }
        
        $order->save();
    }
    

}
