<?php
/**
 * PayOp API Client for WooCommerce Blocks
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp API Client Class
 */
final class PayOp_API_Client {
    
    /**
     * API base URL
     */
    private const API_BASE_URL = 'https://api.payop.com';
    
    /**
     * API credentials
     */
    private string $public_key;
    private string $secret_key;
    private string $jwt_token;
    
    /**
     * Store API request context
     */
    private ?\WP_REST_Request $store_api_request;
    
    /**
     * Constructor
     */
    public function __construct(?\WP_REST_Request $request = null) {
        $this->store_api_request = $request;
        $this->initialize_credentials();
    }
    
    /**
     * Initialize API credentials
     */
    private function initialize_credentials(): void {
        $settings = get_option('woocommerce_payop_blocks_settings', []);
        
        $this->public_key = $settings['public_key'] ?? '';
        $this->secret_key = $settings['secret_key'] ?? '';
        $this->jwt_token = $settings['jwt_token'] ?? '';
        
        if (empty($this->public_key) || empty($this->secret_key) || empty($this->jwt_token)) {
            throw new \Exception(__('PayOp API credentials not configured', 'payop'));
        }
    }
    
    /**
     * Get payment methods optimized for blocks
     */
    public function get_payment_methods_for_blocks(int $application_id): array {
        $methods = $this->get_payment_methods($application_id);
        return $this->format_methods_for_react($methods);
    }
    
    /**
     * Create invoice from Store API context
     */
    public function create_invoice_from_store_api(array $order_data, array $checkout_context): string {
        $invoice_data = $this->prepare_invoice_data($order_data, $checkout_context);
        return $this->create_invoice($invoice_data);
    }
    
    /**
     * Create checkout for blocks
     */
    public function create_checkout_for_blocks(string $invoice_id, array $customer_data, array $additional_fields): array {
        $checkout_data = $this->merge_additional_fields($customer_data, $additional_fields);
        return $this->create_checkout($invoice_id, $checkout_data);
    }
    
    /**
     * Check invoice status for blocks
     */
    public function check_invoice_status_for_blocks(string $invoice_id): array {
        $status = $this->check_invoice_status($invoice_id);
        return $this->format_status_for_react($status);
    }
    
    /**
     * Get payment methods from PayOp API
     */
    private function get_payment_methods(int $application_id): array {
        $endpoint = '/v1/instrument-settings/payment-methods/available-for-application/' . $application_id;
        
        $response = $this->make_request('GET', $endpoint, [], [
            'Authorization' => 'Bearer ' . $this->jwt_token,
        ]);
        
        if (!$response || !isset($response['data'])) {
            throw new \Exception(__('Failed to fetch payment methods from PayOp', 'payop'));
        }
        
        return $response['data'];
    }
    
    /**
     * Create invoice
     */
    private function create_invoice(array $invoice_data): string {
        $endpoint = '/v1/invoices/create';
        
        // Generate signature
        $signature = $this->generate_signature($invoice_data);
        $invoice_data['signature'] = $signature;
        
        $response = $this->make_request('POST', $endpoint, $invoice_data, [
            'Content-Type' => 'application/json',
        ]);
        
        if (!$response || !isset($response['data']['identifier'])) {
            throw new \Exception(__('Failed to create PayOp invoice', 'payop'));
        }
        
        return $response['data']['identifier'];
    }
    
    /**
     * Create checkout
     */
    private function create_checkout(string $invoice_id, array $customer_data): array {
        $endpoint = '/v1/checkout/create';
        
        $checkout_data = [
            'invoiceIdentifier' => $invoice_id,
            'customer' => $customer_data,
        ];
        
        $response = $this->make_request('POST', $endpoint, $checkout_data, [
            'Authorization' => 'Bearer ' . $this->jwt_token,
            'Content-Type' => 'application/json',
        ]);
        
        if (!$response || !isset($response['data'])) {
            throw new \Exception(__('Failed to create PayOp checkout', 'payop'));
        }
        
        return $response['data'];
    }
    
    /**
     * Check invoice status
     */
    private function check_invoice_status(string $invoice_id): array {
        $endpoint = '/v1/checkout/check-invoice-status/' . $invoice_id;
        
        $response = $this->make_request('GET', $endpoint, [], [
            'Authorization' => 'Bearer ' . $this->jwt_token,
        ]);
        
        if (!$response || !isset($response['data'])) {
            throw new \Exception(__('Failed to check PayOp invoice status', 'payop'));
        }
        
        return $response['data'];
    }
    
    /**
     * Make HTTP request to PayOp API
     */
    private function make_request(string $method, string $endpoint, array $data = [], array $headers = []): ?array {
        $url = self::API_BASE_URL . $endpoint;
        
        $args = [
            'method' => $method,
            'headers' => array_merge([
                'User-Agent' => 'PayOp-WooCommerce-Blocks/' . PAYOP_VERSION,
            ], $headers),
            'timeout' => 30,
        ];
        
        if (!empty($data) && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $args['body'] = wp_json_encode($data);
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            wc_get_logger()->error(
                'PayOp API request error: ' . $response->get_error_message(),
                ['source' => 'payop', 'url' => $url]
            );
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $decoded = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            wc_get_logger()->error(
                'PayOp API response JSON decode error: ' . json_last_error_msg(),
                ['source' => 'payop', 'response' => $body]
            );
            return null;
        }
        
        return $decoded;
    }
    
    /**
     * Generate signature for PayOp API
     */
    private function generate_signature(array $data): string {
        ksort($data);
        $string_to_sign = '';
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $string_to_sign .= $key . ':' . $value . ';';
        }
        
        return hash_hmac('sha256', $string_to_sign, $this->secret_key);
    }
    
    /**
     * Format payment methods for React components
     */
    private function format_methods_for_react(array $methods): array {
        return array_map(function($method) {
            return [
                'id' => $method['identifier'],
                'title' => $method['title'],
                'type' => $method['type'],
                'logo' => $method['logo'] ?? '',
                'currencies' => $method['currencies'] ?? [],
                'countries' => $method['countries'] ?? [],
                'fields' => $method['config']['fields'] ?? [],
                'isEnabled' => true,
            ];
        }, $methods);
    }
    
    /**
     * Prepare invoice data
     */
    private function prepare_invoice_data(array $order_data, array $checkout_context): array {
        return [
            'publicKey' => $this->public_key,
            'order' => [
                'id' => $order_data['id'],
                'amount' => $order_data['amount'],
                'currency' => $order_data['currency'],
                'description' => $order_data['description'],
            ],
            'payer' => [
                'email' => $order_data['customer_email'],
                'name' => $order_data['customer_name'],
            ],
            'language' => get_locale(),
            'resultUrl' => $order_data['return_url'],
            'failPath' => $order_data['cancel_url'],
        ];
    }
    
    /**
     * Merge additional fields with customer data
     */
    private function merge_additional_fields(array $customer_data, array $additional_fields): array {
        foreach ($additional_fields as $key => $value) {
            if (strpos($key, 'payop/') === 0) {
                $field_name = str_replace('payop/', '', $key);
                $customer_data[$field_name] = $value;
            }
        }
        
        return $customer_data;
    }
    
    /**
     * Format status for React components
     */
    private function format_status_for_react(array $status): array {
        return [
            'status' => $status['status'] ?? 'unknown',
            'state' => $status['state'] ?? 'unknown',
            'redirectUrl' => $status['redirectUrl'] ?? '',
            'message' => $status['message'] ?? '',
        ];
    }
}
