<?php
/**
 * PayOp Admin Interface
 *
 * @package PayOp\WooCommerce\Blocks\Admin
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks\Admin;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Admin Interface Class
 */
final class PayOp_Admin_Interface {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks(): void {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        add_filter('woocommerce_get_settings_pages', [$this, 'add_settings_page']);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu(): void {
        add_submenu_page(
            'woocommerce',
            __('PayOp Payment Groups', 'payop'),
            __('PayOp Groups', 'payop'),
            'manage_woocommerce',
            'payop-groups',
            [$this, 'render_groups_page']
        );
    }
    
    /**
     * Render payment groups admin page
     */
    public function render_groups_page(): void {
        echo '<div class="wrap">';
        echo '<h1>' . __('PayOp Payment Groups', 'payop') . '</h1>';
        echo '<div id="payop-admin-interface"></div>';
        echo '</div>';
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets(string $hook): void {
        if ('woocommerce_page_payop-groups' !== $hook) {
            return;
        }
        
        // Enqueue React admin interface
        wp_enqueue_script(
            'payop-admin-interface',
            PAYOP_PLUGIN_URL . 'build/admin-interface.js',
            [
                'wp-element',
                'wp-components',
                'wp-api-fetch',
                'wp-i18n',
                'wp-data',
                'wp-notices',
            ],
            PAYOP_VERSION,
            true
        );
        
        wp_enqueue_style(
            'payop-admin-interface',
            PAYOP_PLUGIN_URL . 'build/admin-interface.css',
            ['wp-components'],
            PAYOP_VERSION
        );
        
        // Localize script with admin data
        wp_localize_script('payop-admin-interface', 'payopAdmin', [
            'apiUrl' => rest_url('payop/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'paymentMethods' => $this->get_all_payment_methods_for_admin(),
            'currencies' => $this->get_supported_currencies(),
            'countries' => $this->get_supported_countries(),
            'settings' => get_option('woocommerce_payop_blocks_settings', []),
        ]);
        
        wp_set_script_translations(
            'payop-admin-interface',
            'payop',
            PAYOP_PLUGIN_DIR . 'languages'
        );
    }
    
    /**
     * Add settings page to WooCommerce
     */
    public function add_settings_page(array $settings): array {
        $settings[] = new PayOp_Settings_Page();
        return $settings;
    }
    
    /**
     * Get all payment methods for admin interface
     */
    private function get_all_payment_methods_for_admin(): array {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results(
            "SELECT * FROM $table ORDER BY method_title ASC",
            ARRAY_A
        );
        
        if (!$methods) {
            return [];
        }
        
        return array_map(function($method) {
            return [
                'id' => (int) $method['method_id'],
                'title' => $method['method_title'],
                'type' => $method['method_type'],
                'logo' => $method['method_logo_url'] ?? '',
                'currencies' => json_decode($method['currencies'], true) ?? [],
                'countries' => json_decode($method['countries'], true) ?? [],
                'fields' => json_decode($method['config_fields'], true) ?? [],
                'isEnabled' => (bool) $method['is_enabled'],
            ];
        }, $methods);
    }
    
    /**
     * Get supported currencies
     */
    private function get_supported_currencies(): array {
        return [
            'EUR' => __('Euro', 'payop'),
            'USD' => __('US Dollar', 'payop'),
            'GBP' => __('British Pound', 'payop'),
            'CAD' => __('Canadian Dollar', 'payop'),
            'AUD' => __('Australian Dollar', 'payop'),
            'PHP' => __('Philippine Peso', 'payop'),
            'BRL' => __('Brazilian Real', 'payop'),
            'DKK' => __('Danish Krone', 'payop'),
        ];
    }
    
    /**
     * Get supported countries
     */
    private function get_supported_countries(): array {
        return WC()->countries->get_countries();
    }
}

/**
 * PayOp Settings Page Class
 */
class PayOp_Settings_Page extends \WC_Settings_Page {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->id = 'payop';
        $this->label = __('PayOp', 'payop');
        
        parent::__construct();
    }
    
    /**
     * Get settings array
     */
    public function get_settings(): array {
        return [
            [
                'title' => __('PayOp Payment Gateway Settings', 'payop'),
                'type' => 'title',
                'desc' => __('Configure your PayOp payment gateway settings.', 'payop'),
                'id' => 'payop_settings',
            ],
            [
                'title' => __('Enable/Disable', 'payop'),
                'type' => 'checkbox',
                'label' => __('Enable PayOp Payment Gateway', 'payop'),
                'default' => 'no',
                'id' => 'woocommerce_payop_blocks_settings[enabled]',
            ],
            [
                'title' => __('Title', 'payop'),
                'type' => 'text',
                'description' => __('This controls the title which the user sees during checkout.', 'payop'),
                'default' => __('PayOp Payment Methods', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[title]',
            ],
            [
                'title' => __('Description', 'payop'),
                'type' => 'textarea',
                'description' => __('This controls the description which the user sees during checkout.', 'payop'),
                'default' => __('Pay securely with 122+ payment methods worldwide', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[description]',
            ],
            [
                'title' => __('Public Key', 'payop'),
                'type' => 'text',
                'description' => __('Enter your PayOp public key.', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[public_key]',
            ],
            [
                'title' => __('Secret Key', 'payop'),
                'type' => 'password',
                'description' => __('Enter your PayOp secret key.', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[secret_key]',
            ],
            [
                'title' => __('JWT Token', 'payop'),
                'type' => 'password',
                'description' => __('Enter your PayOp JWT token.', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[jwt_token]',
            ],
            [
                'title' => __('Test Mode', 'payop'),
                'type' => 'checkbox',
                'label' => __('Enable test mode', 'payop'),
                'default' => 'yes',
                'description' => __('Enable this to test payments without processing real transactions.', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[test_mode]',
            ],
            [
                'title' => __('Logging', 'payop'),
                'type' => 'checkbox',
                'label' => __('Enable logging', 'payop'),
                'default' => 'yes',
                'description' => __('Log PayOp events for debugging purposes.', 'payop'),
                'id' => 'woocommerce_payop_blocks_settings[logging]',
            ],
            [
                'type' => 'sectionend',
                'id' => 'payop_settings',
            ],
        ];
    }
}
