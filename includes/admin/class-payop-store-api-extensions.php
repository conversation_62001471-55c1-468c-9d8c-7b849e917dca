<?php
/**
 * PayOp Store API Extensions
 *
 * @package PayOp\WooCommerce\Blocks\Admin
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks\Admin;

use PayOp\WooCommerce\Blocks\PayOp_Payment_Groups;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Store API Extensions Class
 */
final class PayOp_Store_API_Extensions {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks(): void {
        add_action('rest_api_init', [$this, 'register_rest_routes']);
    }
    
    /**
     * Register REST API routes
     */
    public function register_rest_routes(): void {
        // Payment groups endpoints
        register_rest_route('payop/v1', '/groups', [
            'methods' => 'GET',
            'callback' => [$this, 'get_groups'],
            'permission_callback' => [$this, 'check_public_permissions'],
        ]);
        
        register_rest_route('payop/v1', '/groups', [
            'methods' => 'POST',
            'callback' => [$this, 'create_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
            'args' => $this->get_group_schema(),
        ]);
        
        register_rest_route('payop/v1', '/groups/(?P<id>\d+)', [
            'methods' => 'PUT',
            'callback' => [$this, 'update_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
            'args' => $this->get_group_schema(),
        ]);
        
        register_rest_route('payop/v1', '/groups/(?P<id>\d+)', [
            'methods' => 'DELETE',
            'callback' => [$this, 'delete_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
        ]);
        
        // Payment methods endpoints
        register_rest_route('payop/v1', '/methods/(?P<group_id>\d+)', [
            'methods' => 'GET',
            'callback' => [$this, 'get_group_methods'],
            'permission_callback' => [$this, 'check_public_permissions'],
        ]);
        
        register_rest_route('payop/v1', '/methods/assign', [
            'methods' => 'POST',
            'callback' => [$this, 'assign_methods_to_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
            'args' => [
                'group_id' => [
                    'required' => true,
                    'type' => 'integer',
                ],
                'method_ids' => [
                    'required' => true,
                    'type' => 'array',
                ],
            ],
        ]);
    }
    
    /**
     * Get payment groups
     */
    public function get_groups(\WP_REST_Request $request): \WP_REST_Response {
        try {
            $context = [
                'currency' => $request->get_param('currency'),
                'country' => $request->get_param('country'),
                'total' => $request->get_param('total'),
            ];
            
            $groups = PayOp_Payment_Groups::instance()->get_available_groups_for_blocks($context);
            
            return new \WP_REST_Response([
                'success' => true,
                'groups' => $groups,
            ], 200);
            
        } catch (\Exception $e) {
            return new \WP_REST_Response([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Create payment group
     */
    public function create_group(\WP_REST_Request $request): \WP_REST_Response {
        try {
            $group_data = $request->get_params();
            $group_id = PayOp_Payment_Groups::instance()->create_group_from_react($group_data);
            
            return new \WP_REST_Response([
                'success' => true,
                'group_id' => $group_id,
                'message' => __('Payment group created successfully', 'payop'),
            ], 201);
            
        } catch (\Exception $e) {
            return new \WP_REST_Response([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Update payment group
     */
    public function update_group(\WP_REST_Request $request): \WP_REST_Response {
        try {
            $group_id = (int) $request->get_param('id');
            $group_data = $request->get_params();
            
            $success = PayOp_Payment_Groups::instance()->update_group_from_react($group_id, $group_data);
            
            if ($success) {
                return new \WP_REST_Response([
                    'success' => true,
                    'message' => __('Payment group updated successfully', 'payop'),
                ], 200);
            } else {
                return new \WP_REST_Response([
                    'success' => false,
                    'message' => __('Failed to update payment group', 'payop'),
                ], 500);
            }
            
        } catch (\Exception $e) {
            return new \WP_REST_Response([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Delete payment group
     */
    public function delete_group(\WP_REST_Request $request): \WP_REST_Response {
        try {
            $group_id = (int) $request->get_param('id');
            
            $success = PayOp_Payment_Groups::instance()->delete_group($group_id);
            
            if ($success) {
                return new \WP_REST_Response([
                    'success' => true,
                    'message' => __('Payment group deleted successfully', 'payop'),
                ], 200);
            } else {
                return new \WP_REST_Response([
                    'success' => false,
                    'message' => __('Failed to delete payment group', 'payop'),
                ], 500);
            }
            
        } catch (\Exception $e) {
            return new \WP_REST_Response([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Get payment methods for a group
     */
    public function get_group_methods(\WP_REST_Request $request): \WP_REST_Response {
        try {
            $group_id = (int) $request->get_param('group_id');
            $context = [
                'currency' => $request->get_param('currency'),
                'country' => $request->get_param('country'),
                'total' => $request->get_param('total'),
            ];
            
            $methods = PayOp_Payment_Groups::instance()->get_group_methods_for_blocks($group_id, $context);
            
            return new \WP_REST_Response([
                'success' => true,
                'methods' => $methods,
            ], 200);
            
        } catch (\Exception $e) {
            return new \WP_REST_Response([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Assign payment methods to group
     */
    public function assign_methods_to_group(\WP_REST_Request $request): \WP_REST_Response {
        try {
            $group_id = (int) $request->get_param('group_id');
            $method_ids = $request->get_param('method_ids');
            
            $success = PayOp_Payment_Groups::instance()->assign_methods_to_group($group_id, $method_ids);
            
            if ($success) {
                return new \WP_REST_Response([
                    'success' => true,
                    'message' => __('Payment methods assigned successfully', 'payop'),
                ], 200);
            } else {
                return new \WP_REST_Response([
                    'success' => false,
                    'message' => __('Failed to assign payment methods', 'payop'),
                ], 500);
            }
            
        } catch (\Exception $e) {
            return new \WP_REST_Response([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Check public permissions
     */
    public function check_public_permissions(): bool {
        return true; // Public endpoint for frontend
    }
    
    /**
     * Check admin permissions
     */
    public function check_admin_permissions(): bool {
        return current_user_can('manage_woocommerce');
    }
    
    /**
     * Get group schema for validation
     */
    private function get_group_schema(): array {
        return [
            'name' => [
                'type' => 'string',
                'required' => true,
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'description' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
            'displayOrder' => [
                'type' => 'integer',
                'minimum' => 0,
            ],
            'isEnabled' => [
                'type' => 'boolean',
            ],
            'icon' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'color' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_hex_color',
            ],
            'conditions' => [
                'type' => 'object',
                'properties' => [
                    'currencies' => ['type' => 'array'],
                    'countries' => ['type' => 'array'],
                    'min_amount' => ['type' => 'number'],
                    'max_amount' => ['type' => 'number'],
                ],
            ],
        ];
    }
}
