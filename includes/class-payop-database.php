<?php
/**
 * PayOp Database Setup
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Database Class
 */
class PayOp_Database {
    
    /**
     * Create database tables
     */
    public static function create_tables(): void {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Payment methods table
        $payment_methods_table = $wpdb->prefix . 'payop_payment_methods';
        $payment_methods_sql = "CREATE TABLE $payment_methods_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            method_id int(11) NOT NULL,
            method_type enum('bank_transfer','cash','ewallet','cards_international','crypto') NOT NULL,
            method_title varchar(255) NOT NULL,
            method_logo_url text,
            currencies json NOT NULL,
            countries json NOT NULL,
            config_fields json NOT NULL,
            blocks_metadata json,
            is_enabled tinyint(1) DEFAULT 1,
            last_updated datetime NOT NULL,
            cache_expires datetime NOT NULL,
            PRIMARY KEY (id),
            KEY idx_method_id (method_id),
            KEY idx_method_type (method_type),
            KEY idx_enabled (is_enabled),
            KEY idx_cache_expires (cache_expires),
            KEY idx_blocks_enabled (is_enabled, cache_expires)
        ) $charset_collate;";
        
        // Payment groups table
        $payment_groups_table = $wpdb->prefix . 'payop_payment_groups';
        $payment_groups_sql = "CREATE TABLE $payment_groups_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            display_order int(11) DEFAULT 0,
            is_enabled tinyint(1) DEFAULT 1,
            conditions json,
            react_config json,
            created_at datetime NOT NULL,
            updated_at datetime NOT NULL,
            PRIMARY KEY (id),
            KEY idx_enabled (is_enabled),
            KEY idx_display_order (display_order),
            KEY idx_blocks_query (is_enabled, display_order)
        ) $charset_collate;";
        
        // Group methods table
        $group_methods_table = $wpdb->prefix . 'payop_group_methods';
        $group_methods_sql = "CREATE TABLE $group_methods_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            group_id int(11) NOT NULL,
            method_id int(11) NOT NULL,
            display_order int(11) DEFAULT 0,
            is_enabled tinyint(1) DEFAULT 1,
            blocks_config json,
            PRIMARY KEY (id),
            KEY idx_group_id (group_id),
            KEY idx_method_id (method_id),
            KEY idx_enabled (is_enabled),
            KEY idx_blocks_render (group_id, is_enabled, display_order),
            UNIQUE KEY unique_group_method (group_id, method_id),
            FOREIGN KEY (group_id) REFERENCES $payment_groups_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        // Blocks cache table
        $blocks_cache_table = $wpdb->prefix . 'payop_blocks_cache';
        $blocks_cache_sql = "CREATE TABLE $blocks_cache_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            cache_key varchar(255) NOT NULL,
            cache_data json NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_cache_key (cache_key),
            KEY idx_expires (expires_at)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        
        dbDelta($payment_methods_sql);
        dbDelta($payment_groups_sql);
        dbDelta($group_methods_sql);
        dbDelta($blocks_cache_sql);
        
        // Create default payment groups
        self::create_default_groups();
    }
    
    /**
     * Create default payment groups
     */
    private static function create_default_groups(): void {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        $default_groups = [
            [
                'name' => __('Credit & Debit Cards', 'payop'),
                'description' => __('Pay with your credit or debit card', 'payop'),
                'display_order' => 1,
                'react_config' => json_encode(['icon' => 'credit-card', 'color' => '#1e40af']),
            ],
            [
                'name' => __('Bank Transfers', 'payop'),
                'description' => __('Direct bank transfer payments', 'payop'),
                'display_order' => 2,
                'react_config' => json_encode(['icon' => 'bank', 'color' => '#059669']),
            ],
            [
                'name' => __('Digital Wallets', 'payop'),
                'description' => __('Pay with digital wallet services', 'payop'),
                'display_order' => 3,
                'react_config' => json_encode(['icon' => 'wallet', 'color' => '#7c3aed']),
            ],
            [
                'name' => __('Cash Payments', 'payop'),
                'description' => __('Pay with cash at participating locations', 'payop'),
                'display_order' => 4,
                'react_config' => json_encode(['icon' => 'money', 'color' => '#dc2626']),
            ],
            [
                'name' => __('Cryptocurrency', 'payop'),
                'description' => __('Pay with cryptocurrency', 'payop'),
                'display_order' => 5,
                'react_config' => json_encode(['icon' => 'bitcoin', 'color' => '#f59e0b']),
            ],
        ];
        
        foreach ($default_groups as $group) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $groups_table WHERE name = %s",
                $group['name']
            ));
            
            if (!$existing) {
                $group['created_at'] = current_time('mysql');
                $group['updated_at'] = current_time('mysql');
                
                $wpdb->insert($groups_table, $group);
            }
        }
    }
    
    /**
     * Drop tables (for uninstall)
     */
    public static function drop_tables(): void {
        global $wpdb;
        
        $tables = [
            $wpdb->prefix . 'payop_group_methods',
            $wpdb->prefix . 'payop_payment_groups',
            $wpdb->prefix . 'payop_payment_methods',
            $wpdb->prefix . 'payop_blocks_cache',
        ];
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }
}
