<?php
/**
 * PayOp Signature Generator
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Signature Generator Class
 */
final class PayOp_Signature_Generator {
    
    /**
     * Generate PayOp signature using documented formula
     * Formula: SHA256(amount:currency:order_id:secret_key)
     * 
     * @param string $amount Order amount (e.g., "10.00")
     * @param string $currency Currency code (e.g., "EUR")
     * @param string $order_id Unique order identifier
     * @param string $secret_key PayOp secret key
     * @return string SHA-256 signature
     */
    public static function generate(string $amount, string $currency, string $order_id, string $secret_key): string {
        $data = [$amount, $currency, $order_id, $secret_key];
        return hash('sha256', implode(':', $data));
    }
    
    /**
     * Validate signature format
     */
    public static function validate_signature(string $signature): bool {
        return preg_match('/^[a-f0-9]{64}$/', $signature) === 1;
    }
    
    /**
     * Generate signature for invoice data
     */
    public static function generate_for_invoice(array $invoice_data, string $secret_key): string {
        ksort($invoice_data);
        $string_to_sign = '';
        
        foreach ($invoice_data as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $string_to_sign .= $key . ':' . $value . ';';
        }
        
        return hash_hmac('sha256', $string_to_sign, $secret_key);
    }
}
