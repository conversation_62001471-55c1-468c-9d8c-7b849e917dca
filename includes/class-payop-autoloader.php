<?php
/**
 * PayOp Autoloader
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Autoloader Class
 */
class PayOp_Autoloader {
    
    /**
     * Register autoloader
     */
    public static function register(): void {
        spl_autoload_register([__CLASS__, 'autoload']);
    }
    
    /**
     * Autoload classes
     */
    public static function autoload(string $class): void {
        // Check if this is a PayOp class
        if (strpos($class, 'PayOp\\WooCommerce\\Blocks\\') !== 0) {
            return;
        }

        // Remove namespace prefix
        $class_name = str_replace('PayOp\\WooCommerce\\Blocks\\', '', $class);

        // Handle admin namespace
        if (strpos($class_name, 'Admin\\') === 0) {
            $class_name = str_replace('Admin\\', '', $class_name);
            $file_name = self::class_name_to_file_name($class_name);
            $file_path = PAYOP_PLUGIN_DIR . 'includes' . DIRECTORY_SEPARATOR . 'admin' . DIRECTORY_SEPARATOR . $file_name;
        } else {
            // Convert class name to file name
            $file_name = self::class_name_to_file_name($class_name);
            $file_path = PAYOP_PLUGIN_DIR . 'includes' . DIRECTORY_SEPARATOR . $file_name;
        }

        // Load file if it exists
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
    
    /**
     * Convert class name to file name
     */
    private static function class_name_to_file_name(string $class): string {
        // Convert PascalCase to kebab-case with class- prefix
        $file_name = 'class-' . strtolower(preg_replace('/([a-z])([A-Z])/', '$1-$2', $class)) . '.php';

        return $file_name;
    }
}
