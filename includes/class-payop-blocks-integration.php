<?php
/**
 * PayOp Blocks Integration
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only proceed if WooCommerce Blocks is available
if (class_exists('Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry')) {
    use Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry;
}

/**
 * PayOp Blocks Integration Class
 */
final class PayOp_Blocks_Integration {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks(): void {
        add_action('woocommerce_blocks_loaded', [$this, 'register_payment_method']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_block_styles']);
        add_action('woocommerce_blocks_checkout_block_registration', [$this, 'register_checkout_block_integration']);
    }
    
    /**
     * Register payment method with WooCommerce Blocks
     */
    public function register_payment_method(): void {
        if (!class_exists('Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry')) {
            return;
        }

        add_action('woocommerce_blocks_payment_method_type_registration', function($payment_method_registry) {
            $payment_method_registry->register(PayOp_Payment_Method_Type::instance());
        });
    }
    
    /**
     * Enqueue block styles
     */
    public function enqueue_block_styles(): void {
        if (!is_checkout() && !is_cart()) {
            return;
        }
        
        wp_enqueue_style(
            'payop-blocks-style',
            PAYOP_PLUGIN_URL . 'build/payment-method.css',
            [],
            PAYOP_VERSION
        );
    }
    
    /**
     * Register checkout block integration
     */
    public function register_checkout_block_integration($integration_registry): void {
        $integration_registry->register(new PayOp_Checkout_Block_Integration());
    }
}

/**
 * PayOp Checkout Block Integration
 */
class PayOp_Checkout_Block_Integration {
    
    /**
     * Get integration name
     */
    public function get_name(): string {
        return 'payop-checkout-integration';
    }
    
    /**
     * Initialize integration
     */
    public function initialize(): void {
        // Integration initialization logic
    }
    
    /**
     * Get script handles
     */
    public function get_script_handles(): array {
        return ['payop-blocks-payment-method'];
    }
    
    /**
     * Get script data
     */
    public function get_script_data(): array {
        return [
            'payopSettings' => get_option('woocommerce_payop_blocks_settings', []),
            'isTestMode' => get_option('woocommerce_payop_blocks_settings')['test_mode'] ?? 'yes',
        ];
    }
}
