<?php
/**
 * PayOp Payment Methods Management
 *
 * @package PayOp\WooCommerce\Blocks
 */

declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Payment Methods Class
 */
final class PayOp_Payment_Methods {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize hooks if needed
    }
    
    /**
     * Fetch all payment methods from PayOp API
     */
    public function fetch_methods(): array {
        // This would fetch from PayOp API
        // For now, return empty array
        return [];
    }
    
    /**
     * Filter methods by supported currencies
     */
    public function filter_by_currency(string $currency): array {
        $all_methods = $this->get_cached_methods() ?: [];
        
        return array_filter($all_methods, function($method) use ($currency) {
            return in_array($currency, $method['currencies'] ?? []);
        });
    }
    
    /**
     * Filter methods by customer country
     */
    public function filter_by_country(string $country): array {
        $all_methods = $this->get_cached_methods() ?: [];
        
        return array_filter($all_methods, function($method) use ($country) {
            return in_array($country, $method['countries'] ?? []);
        });
    }
    
    /**
     * Get methods for specific payment group
     */
    public function get_methods_for_group(int $group_id): array {
        return PayOp_Payment_Groups::instance()->get_group_methods_for_blocks($group_id);
    }
    
    /**
     * Get required fields configuration for specific payment method
     */
    public function get_method_fields(int $method_id): array {
        $all_methods = $this->get_cached_methods() ?: [];
        
        foreach ($all_methods as $method) {
            if ($method['id'] === $method_id) {
                return $method['fields'] ?? [];
            }
        }
        
        return [];
    }
    
    /**
     * Cache payment methods to reduce API calls
     */
    public function cache_methods(array $methods, int $expiry = 3600): void {
        set_transient('payop_payment_methods', $methods, $expiry);
    }
    
    /**
     * Get cached methods if available and not expired
     */
    public function get_cached_methods(): ?array {
        $cached = get_transient('payop_payment_methods');
        return $cached !== false ? $cached : null;
    }
    
    /**
     * Get all methods for admin interface
     */
    public static function get_all_methods_for_admin(): array {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results(
            "SELECT * FROM $table ORDER BY method_title ASC",
            ARRAY_A
        );
        
        if (!$methods) {
            return [];
        }
        
        return array_map(function($method) {
            return [
                'id' => (int) $method['method_id'],
                'title' => $method['method_title'],
                'type' => $method['method_type'],
                'logo' => $method['method_logo_url'] ?? '',
                'currencies' => json_decode($method['currencies'], true) ?? [],
                'countries' => json_decode($method['countries'], true) ?? [],
                'fields' => json_decode($method['config_fields'], true) ?? [],
                'isEnabled' => (bool) $method['is_enabled'],
            ];
        }, $methods);
    }
    
    /**
     * Get all methods for blocks field registration
     */
    public static function get_all_methods_for_blocks(): array {
        // Return sample method structure for now
        return [
            [
                'identifier' => 'sample_method',
                'config' => [
                    'fields' => [
                        [
                            'name' => 'phone',
                            'type' => 'phone',
                            'title' => __('Phone Number', 'payop'),
                            'required' => true,
                        ],
                        [
                            'name' => 'document',
                            'type' => 'document',
                            'title' => __('Document Number', 'payop'),
                            'required' => false,
                        ],
                    ]
                ]
            ]
        ];
    }
}
