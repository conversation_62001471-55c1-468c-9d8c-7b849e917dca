# PayOp WooCommerce Blocks Payment Gateway

A modern, **WooCommerce Blocks-native** payment gateway plugin for PayOp with 122+ payment methods across 226+ countries. Built exclusively for WooCommerce 8.3+ with React-based components and dynamic payment group management.

## Features

### 🎯 **Blocks-Native Architecture**
- **WooCommerce 8.3+ Exclusive** - No legacy shortcode support
- **React-Based Frontend** - Modern component architecture
- **Store API Integration** - Full integration with WooCommerce's modern API
- **Additional Checkout Fields** - Native WooCommerce field integration

### 💳 **PayOp Integration**
- **122+ Payment Methods** - Comprehensive global coverage
- **226+ Countries** - Worldwide payment support
- **8 Currencies** - EUR, USD, GBP, CAD, AUD, PHP, BRL, DKK
- **Direct Integration** - Bypass PayOp hosted checkout

### 🎨 **Dynamic Payment Groups**
- **Admin-Configurable Groups** - Visual drag-and-drop interface
- **Real-time Filtering** - Context-aware payment method display
- **Conditional Logic** - Show/hide based on currency, country, cart total
- **Performance Optimized** - Efficient caching and lazy loading

### ⚡ **Modern Development**
- **React Components** - Modern frontend architecture
- **TypeScript Support** - Type-safe development
- **Webpack Build Process** - Optimized asset compilation
- **WordPress Standards** - Following latest WP/WC patterns

## Requirements

- **WordPress**: 6.4+
- **WooCommerce**: 8.3+ (Blocks-based checkout)
- **PHP**: 8.2+
- **Node.js**: 16+ (for development)

## Installation

### From Source

1. Clone the repository:
```bash
git clone https://github.com/payop/woocommerce-blocks-gateway.git payop-woocommerce-blocks
cd payop-woocommerce-blocks
```

2. Install dependencies:
```bash
npm install
```

3. Build the assets:
```bash
npm run build
```

4. Upload to your WordPress plugins directory or install via WordPress admin.

5. Activate the plugin through the WordPress admin.

## Configuration

### 1. Basic Settings

Navigate to **WooCommerce > Settings > PayOp** and configure:

- **Enable/Disable** - Enable the PayOp payment gateway
- **Title** - Payment method title shown to customers
- **Description** - Payment method description
- **Public Key** - Your PayOp public key
- **Secret Key** - Your PayOp secret key
- **JWT Token** - Your PayOp JWT token
- **Test Mode** - Enable for testing (default: enabled)

### 2. Payment Groups Management

Navigate to **WooCommerce > PayOp Groups** to:

- Create custom payment groups
- Assign payment methods to groups
- Configure conditional display rules
- Set group icons and colors
- Manage display order

### 3. Payment Methods

The plugin automatically syncs available payment methods from PayOp API based on your account configuration.

## Development

### Setup Development Environment

1. Clone and install dependencies:
```bash
git clone https://github.com/payop/woocommerce-blocks-gateway.git
cd payop-woocommerce-blocks
npm install
```

2. Start development server:
```bash
npm run start
```

3. Build for production:
```bash
npm run build
```

### Project Structure

```
payop-woocommerce-blocks/
├── includes/                 # PHP classes
│   ├── class-payop-payment-method-type.php
│   ├── class-payop-blocks-integration.php
│   ├── class-payop-api-client.php
│   ├── class-payop-payment-groups.php
│   ├── class-payop-additional-fields.php
│   └── admin/               # Admin classes
├── src/                     # React source files
│   ├── payment-method/      # Frontend components
│   └── admin/              # Admin interface
├── build/                   # Compiled assets
├── assets/                  # Static assets
└── languages/              # Translation files
```

### Key Classes

- **PayOp_Payment_Method_Type** - Extends `AbstractPaymentMethodType`
- **PayOp_Blocks_Integration** - Registers payment method with WooCommerce Blocks
- **PayOp_API_Client** - Handles PayOp API communication
- **PayOp_Payment_Groups** - Manages dynamic payment groups
- **PayOp_Additional_Fields** - Integrates with WooCommerce additional checkout fields

### React Components

- **PayOpPaymentMethod** - Main payment method component
- **PaymentGroupSelector** - Payment group selection interface
- **PaymentMethodGrid** - Payment method display grid
- **AdditionalFieldsRenderer** - Dynamic field rendering
- **PayOpAdminInterface** - Admin management interface

## API Integration

### PayOp API Endpoints

The plugin integrates with these PayOp API endpoints:

- `GET /v1/instrument-settings/payment-methods/available-for-application/{ID}` - Fetch payment methods
- `POST /v1/invoices/create` - Create payment invoice
- `POST /v1/checkout/create` - Create checkout session
- `GET /v1/checkout/check-invoice-status/{invoiceID}` - Check payment status

### Store API Extensions

Custom REST API endpoints for frontend integration:

- `GET /wp-json/payop/v1/groups` - Get available payment groups
- `GET /wp-json/payop/v1/methods/{group_id}` - Get methods for group
- `POST /wp-json/payop/v1/groups` - Create payment group (admin)
- `PUT /wp-json/payop/v1/groups/{id}` - Update payment group (admin)

## Security

### Data Protection
- API credentials encrypted in database
- Secure signature generation for PayOp API
- Input sanitization and validation
- CSRF protection via WordPress nonces

### IPN Validation
- IP address validation from PayOp servers
- Signature verification for webhook payloads
- Order status validation before processing

## Performance

### Optimization Features
- Payment method caching (1-hour TTL)
- React component memoization
- Lazy loading of payment methods
- Efficient database queries with proper indexing
- Store API caching for frequently accessed data

## Troubleshooting

### Common Issues

**Plugin not appearing in checkout:**
- Ensure WooCommerce 8.3+ is installed
- Verify blocks-based checkout is enabled
- Check PayOp API credentials are configured

**Payment methods not loading:**
- Check PayOp API credentials
- Verify internet connectivity
- Review error logs in WooCommerce > Status > Logs

**React components not rendering:**
- Ensure assets are built (`npm run build`)
- Check browser console for JavaScript errors
- Verify WordPress/WooCommerce versions

### Debug Mode

Enable debug logging in plugin settings and check logs at:
**WooCommerce > Status > Logs > payop-{date}.log**

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Coding Standards

- Follow WordPress coding standards
- Use TypeScript for React components
- Add PHPDoc comments for all functions
- Include unit tests for new features

## License

This plugin is licensed under the GPL v2 or later.

## Support

- **Documentation**: [PayOp Developer Docs](https://payop.com/docs)
- **Support**: [PayOp Support](https://payop.com/support)
- **Issues**: [GitHub Issues](https://github.com/payop/woocommerce-blocks-gateway/issues)

## Changelog

### 1.0.0
- Initial release
- WooCommerce Blocks-native architecture
- Dynamic payment groups management
- React-based admin interface
- 122+ payment methods support
- Store API integration
