<?php
/**
 * Plugin Name: PayOp WooCommerce Blocks Payment Gateway
 * Plugin URI: https://payop.com
 * Description: WooCommerce Blocks-native payment gateway for PayOp with 122+ payment methods. Requires WooCommerce 8.3+.
 * Version: 1.0.0
 * Author: PayOp
 * Author URI: https://payop.com
 * Text Domain: payop
 * Domain Path: /languages
 * Requires at least: 6.4
 * Tested up to: 6.4
 * Requires PHP: 8.2
 * WC requires at least: 8.3
 * WC tested up to: 8.3
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PAYOP_VERSION', '1.0.0');
define('PAYOP_PLUGIN_FILE', __FILE__);
define('PAYOP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PAYOP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PAYOP_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main PayOp Plugin Class
 */
final class PayOp_WooCommerce_Blocks {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks(): void {
        add_action('plugins_loaded', [$this, 'init_plugin'], 20); // Load after WooCommerce
        add_action('init', [$this, 'load_textdomain']);

        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }
    
    /**
     * Initialize plugin
     */
    public function init_plugin(): void {
        // Only proceed if we're not in the middle of plugin activation
        if (defined('WP_INSTALLING') && WP_INSTALLING) {
            return;
        }
        
        // Check requirements
        if (!$this->check_requirements()) {
            return;
        }
        
        // Load autoloader
        $this->load_autoloader();
        
        // Initialize plugin components
        $this->init_components();
    }
    
    /**
     * Check plugin requirements
     */
    private function check_requirements(): bool {
        // Check PHP version
        if (version_compare(PHP_VERSION, '8.2.0', '<')) {
            $this->add_admin_notice(sprintf(
                __('PayOp requires PHP 8.2 or higher. You are running PHP %s.', 'payop'),
                PHP_VERSION
            ), 'error');
            return false;
        }
        
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '6.4.0', '<')) {
            $this->add_admin_notice(
                __('PayOp requires WordPress 6.4 or higher.', 'payop'),
                'error'
            );
            return false;
        }
        
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            $this->add_admin_notice(
                __('PayOp requires WooCommerce to be installed and active.', 'payop'),
                'error'
            );
            return false;
        }
        
        // Check WooCommerce version
        if (version_compare(WC_VERSION, '8.3.0', '<')) {
            $this->add_admin_notice(sprintf(
                __('PayOp requires WooCommerce 8.3 or higher for blocks-based checkout. You are running WooCommerce %s.', 'payop'),
                WC_VERSION
            ), 'error');
            return false;
        }
        
        // Check if WooCommerce Blocks is available
        if (!function_exists('woocommerce_register_additional_checkout_field')) {
            $this->add_admin_notice(
                __('PayOp requires WooCommerce Blocks functionality. Please ensure WooCommerce Blocks is active.', 'payop'),
                'error'
            );
            return false;
        }
        
        return true;
    }
    
    /**
     * Load autoloader and classes
     */
    private function load_autoloader(): void {
        // Load core classes in dependency order
        $core_classes = [
            'includes/class-payop-database.php',
            'includes/class-payop-signature-generator.php',
            'includes/class-payop-payment-methods.php',
            'includes/class-payop-payment-groups.php',
            'includes/class-payop-api-client.php',
            'includes/class-payop-additional-fields.php',
            'includes/class-payop-payment-method-type.php',
            'includes/class-payop-blocks-integration.php',
        ];

        foreach ($core_classes as $class_file) {
            $file_path = PAYOP_PLUGIN_DIR . $class_file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        // Load admin classes
        if (is_admin()) {
            $admin_classes = [
                'includes/admin/class-payop-admin-interface.php',
                'includes/admin/class-payop-store-api-extensions.php',
            ];

            foreach ($admin_classes as $class_file) {
                $file_path = PAYOP_PLUGIN_DIR . $class_file;
                if (file_exists($file_path)) {
                    require_once $file_path;
                }
            }
        }

        // Register autoloader for any remaining classes
        require_once PAYOP_PLUGIN_DIR . 'includes/class-payop-autoloader.php';
        PayOp_Autoloader::register();
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components(): void {
        try {
            // Initialize core components safely
            if (class_exists('PayOp\WooCommerce\Blocks\PayOp_Payment_Groups')) {
                PayOp\WooCommerce\Blocks\PayOp_Payment_Groups::instance();
            }
            
            if (class_exists('PayOp\WooCommerce\Blocks\PayOp_Payment_Methods')) {
                PayOp\WooCommerce\Blocks\PayOp_Payment_Methods::instance();
            }
            
            if (class_exists('PayOp\WooCommerce\Blocks\PayOp_Additional_Fields')) {
                PayOp\WooCommerce\Blocks\PayOp_Additional_Fields::instance();
            }

            // Only initialize blocks components if WooCommerce Blocks is available
            if (class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
                if (class_exists('PayOp\WooCommerce\Blocks\PayOp_Payment_Method_Type')) {
                    PayOp\WooCommerce\Blocks\PayOp_Payment_Method_Type::instance();
                }
                
                if (class_exists('PayOp\WooCommerce\Blocks\PayOp_Blocks_Integration')) {
                    PayOp\WooCommerce\Blocks\PayOp_Blocks_Integration::instance();
                }
            }

            // Initialize admin components
            if (is_admin()) {
                if (class_exists('PayOp\WooCommerce\Blocks\Admin\PayOp_Admin_Interface')) {
                    PayOp\WooCommerce\Blocks\Admin\PayOp_Admin_Interface::instance();
                }
                
                if (class_exists('PayOp\WooCommerce\Blocks\Admin\PayOp_Store_API_Extensions')) {
                    PayOp\WooCommerce\Blocks\Admin\PayOp_Store_API_Extensions::instance();
                }
            }
        } catch (\Exception $e) {
            $this->add_admin_notice(sprintf(
                __('PayOp plugin initialization error: %s', 'payop'),
                $e->getMessage()
            ), 'error');
        }
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain(): void {
        load_plugin_textdomain(
            'payop',
            false,
            dirname(PAYOP_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate(): void {
        // Check requirements on activation
        if (!$this->check_requirements()) {
            wp_die(__('PayOp plugin requirements not met. Please check the error messages above.', 'payop'));
        }
        
        // Create database tables (only if database class exists)
        if (class_exists('PayOp_Database')) {
            $this->create_database_tables();
        }
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate(): void {
        // Clear scheduled events
        wp_clear_scheduled_hook('payop_sync_payment_methods');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_database_tables(): void {
        if (class_exists('PayOp_Database')) {
            PayOp_Database::create_tables();
        }
    }
    
    /**
     * Set default options
     */
    private function set_default_options(): void {
        $default_options = [
            'enabled' => 'no',
            'title' => __('PayOp Payment Methods', 'payop'),
            'description' => __('Pay securely with 122+ payment methods worldwide', 'payop'),
            'public_key' => '',
            'secret_key' => '',
            'jwt_token' => '',
            'test_mode' => 'yes',
            'logging' => 'yes',
        ];
        
        add_option('woocommerce_payop_blocks_settings', $default_options);
    }
    
    /**
     * Add admin notice safely
     */
    private function add_admin_notice(string $message, string $type = 'error'): void {
        // Only show notices in admin area and not during AJAX requests
        if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
            return;
        }
        
        add_action('admin_notices', function() use ($message, $type) {
            printf(
                '<div class="notice notice-%s"><p>%s</p></div>',
                esc_attr($type),
                esc_html($message)
            );
        });
    }
}

// Initialize plugin
PayOp_WooCommerce_Blocks::instance();
