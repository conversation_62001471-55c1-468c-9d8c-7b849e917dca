<?php
/**
 * Plugin Name: PayOp WooCommerce Blocks Payment Gateway
 * Plugin URI: https://payop.com
 * Description: WooCommerce Blocks-native payment gateway for PayOp with 122+ payment methods. Requires WooCommerce 8.3+.
 * Version: 1.0.0
 * Author: PayOp
 * Author URI: https://payop.com
 * Text Domain: payop
 * Domain Path: /languages
 * Requires at least: 6.4
 * Tested up to: 6.4
 * Requires PHP: 8.2
 * WC requires at least: 8.3
 * WC tested up to: 8.3
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PAYOP_VERSION', '1.0.0');
define('PAYOP_PLUGIN_FILE', __FILE__);
define('PAYOP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PAYOP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PAYOP_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main PayOp Plugin Class
 */
final class PayOp_WooCommerce_Blocks {
    
    /**
     * Plugin instance
     */
    private static ?self $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks(): void {
        add_action('plugins_loaded', [$this, 'init_plugin']);
        add_action('init', [$this, 'load_textdomain']);
        
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }
    
    /**
     * Initialize plugin
     */
    public function init_plugin(): void {
        // Check requirements
        if (!$this->check_requirements()) {
            return;
        }
        
        // Load autoloader
        $this->load_autoloader();
        
        // Initialize plugin components
        $this->init_components();
    }
    
    /**
     * Check plugin requirements
     */
    private function check_requirements(): bool {
        // Check PHP version
        if (version_compare(PHP_VERSION, '8.2.0', '<')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('PayOp requires PHP 8.2 or higher. You are running PHP %s.', 'payop'),
                    PHP_VERSION
                );
                echo '</p></div>';
            });
            return false;
        }
        
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '6.4.0', '<')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo __('PayOp requires WordPress 6.4 or higher.', 'payop');
                echo '</p></div>';
            });
            return false;
        }
        
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo __('PayOp requires WooCommerce to be installed and active.', 'payop');
                echo '</p></div>';
            });
            return false;
        }
        
        // Check WooCommerce version
        if (version_compare(WC_VERSION, '8.3.0', '<')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('PayOp requires WooCommerce 8.3 or higher for blocks-based checkout. You are running WooCommerce %s.', 'payop'),
                    WC_VERSION
                );
                echo '</p></div>';
            });
            return false;
        }
        
        // Check if WooCommerce Blocks is available
        if (!function_exists('woocommerce_register_additional_checkout_field')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo __('PayOp requires WooCommerce Blocks functionality. Please ensure WooCommerce Blocks is active.', 'payop');
                echo '</p></div>';
            });
            return false;
        }
        
        return true;
    }
    
    /**
     * Load autoloader
     */
    private function load_autoloader(): void {
        require_once PAYOP_PLUGIN_DIR . 'includes/class-payop-autoloader.php';
        PayOp_Autoloader::register();
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components(): void {
        // Initialize core components
        PayOp\WooCommerce\Blocks\PayOp_Payment_Method_Type::instance();
        PayOp\WooCommerce\Blocks\PayOp_Blocks_Integration::instance();
        PayOp\WooCommerce\Blocks\PayOp_Additional_Fields::instance();
        PayOp\WooCommerce\Blocks\PayOp_Payment_Groups::instance();
        
        // Initialize admin components
        if (is_admin()) {
            PayOp\WooCommerce\Blocks\Admin\PayOp_Admin_Interface::instance();
            PayOp\WooCommerce\Blocks\Admin\PayOp_Store_API_Extensions::instance();
        }
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain(): void {
        load_plugin_textdomain(
            'payop',
            false,
            dirname(PAYOP_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate(): void {
        // Check requirements on activation
        if (!$this->check_requirements()) {
            wp_die(__('PayOp plugin requirements not met. Please check the error messages above.', 'payop'));
        }
        
        // Create database tables
        $this->create_database_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate(): void {
        // Clear scheduled events
        wp_clear_scheduled_hook('payop_sync_payment_methods');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_database_tables(): void {
        require_once PAYOP_PLUGIN_DIR . 'includes/class-payop-database.php';
        PayOp_Database::create_tables();
    }
    
    /**
     * Set default options
     */
    private function set_default_options(): void {
        $default_options = [
            'enabled' => 'no',
            'title' => __('PayOp Payment Methods', 'payop'),
            'description' => __('Pay securely with 122+ payment methods worldwide', 'payop'),
            'public_key' => '',
            'secret_key' => '',
            'jwt_token' => '',
            'test_mode' => 'yes',
            'logging' => 'yes',
        ];
        
        add_option('woocommerce_payop_blocks_settings', $default_options);
    }
}

// Initialize plugin
PayOp_WooCommerce_Blocks::instance();
