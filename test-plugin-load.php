<?php
/**
 * Test PayOp Plugin Loading
 * Run this file to test if the plugin can be loaded without errors
 */

// Simulate WordPress environment
define('ABSPATH', '/Users/<USER>/Local Sites/test/app/public/');
define('WP_DEBUG', true);

// Define plugin constants
define('PAYOP_PLUGIN_FILE', __DIR__ . '/payop-woocommerce-blocks.php');
define('PAYOP_PLUGIN_DIR', __DIR__ . '/');
define('PAYOP_PLUGIN_URL', 'http://test.local/wp-content/plugins/payop-direct-payment-woocommerce/');
define('PAYOP_VERSION', '1.0.0');

// Mock WordPress functions
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $args = 1) {
        echo "add_action called: $hook\n";
    }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $args = 1) {
        echo "add_filter called: $hook\n";
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        echo "register_activation_hook called\n";
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        echo "register_deactivation_hook called\n";
    }
}

if (!function_exists('is_admin')) {
    function is_admin() {
        return false;
    }
}

if (!function_exists('version_compare')) {
    function version_compare($version1, $version2, $operator = null) {
        return \version_compare($version1, $version2, $operator);
    }
}

// Mock WooCommerce
if (!defined('WC_VERSION')) {
    define('WC_VERSION', '8.3.0');
}

echo "Testing PayOp Plugin Loading...\n";
echo "================================\n";

try {
    // Test autoloader
    require_once __DIR__ . '/includes/class-payop-autoloader.php';
    echo "✓ Autoloader loaded successfully\n";
    
    PayOp_Autoloader::register();
    echo "✓ Autoloader registered successfully\n";
    
    // Test core classes
    require_once __DIR__ . '/includes/class-payop-database.php';
    echo "✓ Database class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-payment-groups.php';
    echo "✓ Payment Groups class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-payment-methods.php';
    echo "✓ Payment Methods class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-additional-fields.php';
    echo "✓ Additional Fields class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-payment-method-type.php';
    echo "✓ Payment Method Type class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-blocks-integration.php';
    echo "✓ Blocks Integration class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-api-client.php';
    echo "✓ API Client class loaded\n";
    
    require_once __DIR__ . '/includes/class-payop-signature-generator.php';
    echo "✓ Signature Generator class loaded\n";
    
    // Test class instantiation
    $payment_groups = PayOp\WooCommerce\Blocks\PayOp_Payment_Groups::instance();
    echo "✓ Payment Groups instance created\n";
    
    $payment_methods = PayOp\WooCommerce\Blocks\PayOp_Payment_Methods::instance();
    echo "✓ Payment Methods instance created\n";
    
    echo "\n================================\n";
    echo "✅ All classes loaded successfully!\n";
    echo "The plugin should now activate without errors.\n";
    
} catch (Exception $e) {
    echo "\n================================\n";
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "\n================================\n";
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
}
