{"name": "payop-woocommerce-blocks", "version": "1.0.0", "description": "WooCommerce Blocks-native payment gateway for PayOp with 122+ payment methods", "main": "index.js", "scripts": {"build": "wp-scripts build", "start": "wp-scripts start", "dev": "wp-scripts start", "lint:js": "wp-scripts lint-js", "lint:css": "wp-scripts lint-style", "format": "wp-scripts format", "test": "wp-scripts test-unit-js"}, "keywords": ["woocommerce", "blocks", "payment", "gateway", "payop", "react"], "author": "PayOp", "license": "GPL-2.0-or-later", "devDependencies": {"@wordpress/scripts": "^26.0.0"}, "dependencies": {"@wordpress/element": "^5.0.0", "@wordpress/i18n": "^4.0.0", "@wordpress/components": "^25.0.0", "@wordpress/data": "^9.0.0", "@wordpress/api-fetch": "^6.0.0", "@wordpress/notices": "^4.0.0", "@wordpress/html-entities": "^3.0.0", "@woocommerce/blocks-registry": "^8.0.0", "@woocommerce/settings": "^1.0.0", "react-beautiful-dnd": "^13.1.1"}, "browserslist": ["extends @wordpress/browserslist-config"]}