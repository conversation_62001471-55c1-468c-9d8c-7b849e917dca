# PayOp WooCommerce Blocks-Native Plugin Development Plan

## Project Overview

Develop a **WooCommerce Blocks-native** payment gateway plugin using PayOp's **Direct Integration API** with 122 payment methods across 226+ countries. The plugin is built exclusively for **WooCommerce block-based checkout** (WooCommerce 8.3+) with dynamic payment groups, React-based components, and **direct payment provider redirection**, completely bypassing PayOp's hosted checkout page.

**Core Architecture Requirements**:
- **Blocks-Only Implementation**: Exclusively built for WooCommerce Blocks using `AbstractPaymentMethodType` and `registerPaymentMethod()`
- **React-Based Frontend**: Modern React components for payment method selection and dynamic field rendering
- **Dynamic Payment Groups**: Admin-configurable payment method grouping with visual interface
- **Native Additional Fields**: Integration with WooCommerce's `woocommerce_register_additional_checkout_field` API
- **Direct Integration Only**: Skip PayOp's hosted checkout entirely and redirect customers directly to payment providers
- **Minimum WooCommerce 8.3+**: No legacy shortcode compatibility - blocks-first architecture

**Excluded Functionality**:
- Legacy shortcode-based checkout support (removed completely)
- PayOp hosted checkout page (completely bypassed)
- Automated refund processing (handled manually)
- Withdrawal functionality (handled manually)
- Subscription payments (PayOp supports one-time payments only)
- Backward compatibility with WooCommerce < 8.3

## Technical Foundation

### Core Requirements
- **PHP**: 8.2+ with modern OOP patterns and strict typing
- **WordPress**: 6.4+ with full block editor support
- **WooCommerce**: 8.3+ (blocks-based checkout required)
- **WooCommerce Blocks**: Exclusive integration target - no legacy support
- **React**: 18+ for frontend components (via WooCommerce Blocks)
- **PayOp API**: Direct Integration (122 methods analyzed)
- **Architecture**: Blocks-native, React-based, modern WooCommerce patterns only

### WooCommerce Blocks-Native Architecture (WooCommerce 8.3+ Exclusive)
**Critical Requirement**: This plugin is built exclusively for WooCommerce block-based checkout with no legacy compatibility. The implementation follows modern WooCommerce Blocks patterns:

**Blocks-Native Integration Flow:**
1. **Server-Side Registration**: Extend `AbstractPaymentMethodType` exclusively - no `WC_Payment_Gateway` usage
2. **Client-Side Registration**: Use `registerPaymentMethod()` with React components only
3. **Dynamic Payment Groups**: Admin-configurable grouping of 122 payment methods with React-based interface
4. **Native Additional Fields**: Use `woocommerce_register_additional_checkout_field` for all dynamic payment data
5. **React Components**: Modern React-based payment method selection and field rendering
6. **Real-time Validation**: JSON Schema-based conditional field validation via WooCommerce Blocks API
7. **Store API Integration**: Process payments via WooCommerce Store API exclusively

**PayOp Direct Integration Flow (Blocks-Native):**
1. **Invoice Creation**: Use `POST /v1/invoices/create` with proper signature via Store API context
2. **Dynamic Field Collection**: Collect payment method-specific fields using WooCommerce additional checkout fields
3. **Checkout Creation**: Use `POST /v1/checkout/create` to get direct payment provider URL
4. **Direct Redirect**: Redirect customer directly to payment provider (NOT to PayOp checkout)
5. **IPN Handling**: Process payment notifications via Store API endpoints with proper validation

### API Integration Specifications
Based on factual API documentation analysis:
- **Payment Methods**: 122 total (68 bank_transfer, 42 cash, 10 ewallet, 1 cards_international, 1 crypto)
- **Geographic Coverage**: 226+ countries
- **Currency Support**: 8 currencies (EUR: 92 methods, USD: 18 methods, PHP: 10 methods, etc.)
- **Field Requirements**: Dynamic (email: 122, name: 120, phone: 48, document: 37, etc.)
- **API Base URL**: `https://api.payop.com` (from documented endpoints)
- **Payment Types**: One-time payments only (no subscriptions)

### Blocks-Native Dynamic Payment Groups Architecture
**Core Requirement**: React-based, admin-configurable payment grouping system built for WooCommerce Blocks:
- **React Admin Interface**: Modern React-based payment method management with drag-and-drop grouping
- **Block-Aware Group Configuration**: Groups designed specifically for block-based checkout display
- **Method Assignment**: Flexible assignment of payment methods to multiple groups with block-specific metadata
- **Conditional Display**: Real-time show/hide groups based on cart context using WooCommerce Blocks data
- **Performance Optimization**: Efficient caching and lazy loading optimized for block-based checkout rendering
- **Store API Integration**: Group data exposed via WooCommerce Store API for React components

## Development Phases

### Phase 1: Blocks-Native Foundation & Architecture (Week 1-2)

#### 1.1 Blocks-Only Plugin Architecture Setup
**Deliverables:**
- Main plugin file with WooCommerce 8.3+ requirement enforcement
- Modern autoloader implementation with strict typing
- Namespace structure (`PayOp\WooCommerce\Blocks\`)
- Constants definition optimized for blocks architecture
- **Blocks-exclusive integration foundation**

**Blocks-Native File Structure:**
```
payop-woocommerce-blocks/
├── payop-woocommerce-blocks.php (main file - blocks only)
├── includes/
│   ├── class-payop-payment-method-type.php (extends AbstractPaymentMethodType)
│   ├── class-payop-blocks-integration.php (blocks registration & management)
│   ├── class-payop-api-client.php (Store API integration)
│   ├── class-payop-payment-methods.php (blocks-optimized)
│   ├── class-payop-payment-groups.php (React-based groups)
│   ├── class-payop-additional-fields.php (WooCommerce additional fields integration)
│   ├── class-payop-signature-generator.php
│   └── admin/
│       ├── class-payop-admin-interface.php (React-based admin)
│       ├── class-payop-group-manager.php (React group builder)
│       └── class-payop-store-api-extensions.php (Store API endpoints)
├── src/ (React source files)
│   ├── payment-method/
│   │   ├── index.js (payment method registration)
│   │   ├── payment-method.js (main React component)
│   │   ├── payment-groups.js (group selection component)
│   │   └── dynamic-fields.js (additional fields component)
│   └── admin/
│       ├── group-builder.js (React admin interface)
│       └── method-manager.js (payment method management)
├── build/ (compiled React assets)
│   ├── payment-method.js
│   ├── payment-method.css
│   ├── admin-interface.js
│   └── admin-interface.css
├── assets/
│   ├── css/ (additional styles)
│   └── images/
└── languages/
```

#### 1.2 Blocks-Native Configuration Management
**Implementation:**
- Secure credential storage with WooCommerce 8.3+ encryption standards
- Store API endpoint configuration for blocks integration
- Modern logging system with WooCommerce HPOS support
- Payment method caching optimized for blocks rendering
- **React-based dynamic payment group configuration storage**
- **Blocks-exclusive settings with no legacy options**
- **Store API data exposure configuration**

**Security Considerations:**
- Encrypt sensitive data using WooCommerce 8.3+ security patterns
- Implement proper REST API nonce verification for Store API
- Sanitize all inputs with modern WordPress/WooCommerce validation
- Validate API responses with strict typing
- **Secure React component data transmission**
- **Store API endpoint security hardening**

#### 1.3 WooCommerce Blocks-Exclusive Registration
**Blocks-Only Integration Approach:**

**Primary Payment Method Type Class (Blocks-Native):**
```php
<?php
declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;

final class PayOp_Payment_Method_Type extends AbstractPaymentMethodType {
    protected $name = 'payop';

    public function initialize(): void {
        $this->settings = get_option('woocommerce_payop_blocks_settings', []);

        // Enforce WooCommerce 8.3+ requirement
        if (version_compare(WC_VERSION, '8.3.0', '<')) {
            throw new \Exception('PayOp requires WooCommerce 8.3+ for blocks-based checkout');
        }
    }

    public function is_active(): bool {
        return filter_var($this->get_setting('enabled', false), FILTER_VALIDATE_BOOLEAN)
            && $this->is_blocks_checkout_available();
    }

    public function get_payment_method_script_handles(): array {
        wp_register_script(
            'payop-blocks-payment-method',
            plugins_url('build/payment-method.js', PAYOP_PLUGIN_FILE),
            ['wc-blocks-registry', 'wc-settings', 'wp-element', 'wp-html-entities'],
            PAYOP_VERSION,
            true
        );

        return ['payop-blocks-payment-method'];
    }

    public function get_payment_method_data(): array {
        return [
            'title' => $this->get_setting('title'),
            'description' => $this->get_setting('description'),
            'supports' => ['products'], // Blocks-specific features only
            'paymentGroups' => $this->get_payment_groups_for_blocks(),
            'additionalFields' => $this->get_additional_fields_config(),
            'apiEndpoints' => $this->get_store_api_endpoints(),
        ];
    }

    private function is_blocks_checkout_available(): bool {
        return function_exists('woocommerce_register_additional_checkout_field');
    }

    private function get_payment_groups_for_blocks(): array {
        // Return groups optimized for React components
        return PayOp_Payment_Groups::get_groups_for_blocks();
    }
}
```

### Phase 2: Store API Integration & Blocks-Native Payment Groups (Week 2-3)

#### 2.1 Store API-Integrated PayOp Client Implementation
**Blocks-Native Core Functionality:**
- HTTP client optimized for WooCommerce Store API context
- Signature generation integrated with blocks checkout flow
- JWT token management for Store API operations
- Response validation with strict typing for React components
- **Store API endpoint integration for blocks checkout**

**Blocks-Optimized API Client Structure:**
```php
<?php
declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

final class PayOp_API_Client {
    private string $public_key;
    private string $secret_key;
    private string $jwt_token;
    private \WP_REST_Request $store_api_request;

    public function __construct(\WP_REST_Request $request = null) {
        $this->store_api_request = $request;
        $this->initialize_credentials();
    }

    // Blocks-optimized payment methods retrieval
    public function get_payment_methods_for_blocks(int $application_id): array {
        $methods = $this->get_payment_methods($application_id);
        return $this->format_methods_for_react($methods);
    }

    // Store API context-aware invoice creation
    public function create_invoice_from_store_api(array $order_data, array $checkout_context): string {
        $invoice_data = $this->prepare_invoice_data($order_data, $checkout_context);
        return $this->create_invoice($invoice_data);
    }

    // Blocks checkout integration
    public function create_checkout_for_blocks(string $invoice_id, array $customer_data, array $additional_fields): array {
        $checkout_data = $this->merge_additional_fields($customer_data, $additional_fields);
        return $this->create_checkout($invoice_id, $checkout_data);
    }

    // React-optimized status checking
    public function check_invoice_status_for_blocks(string $invoice_id): array {
        $status = $this->check_invoice_status($invoice_id);
        return $this->format_status_for_react($status);
    }

    // Core PayOp API methods (unchanged)
    private function get_payment_methods(int $application_id): array { /* ... */ }
    private function create_invoice(array $order_data): string { /* ... */ }
    private function create_checkout(string $invoice_id, array $customer_data): array { /* ... */ }
    private function check_invoice_status(string $invoice_id): array { /* ... */ }
    private function get_invoice_info(string $invoice_id): array { /* ... */ }
    private function get_transaction_details(string $transaction_id): array { /* ... */ }
    private function void_transaction(string $invoice_id): bool { /* ... */ }

    // Blocks-specific helper methods
    private function format_methods_for_react(array $methods): array {
        // Format payment methods for React component consumption
    }

    private function merge_additional_fields(array $customer_data, array $additional_fields): array {
        // Merge WooCommerce additional checkout fields with customer data
    }
}
```

#### 2.2 Signature Generation System
**Implementation based on API documentation:**
```php
class PayOp_Signature_Generator {
    /**
     * Generate PayOp signature using documented formula
     * Formula: SHA256(amount:currency:order_id:secret_key)
     * 
     * @param string $amount Order amount (e.g., "10.00")
     * @param string $currency Currency code (e.g., "EUR")
     * @param string $order_id Unique order identifier
     * @param string $secret_key PayOp secret key
     * @return string SHA-256 signature
     */
    public static function generate(string $amount, string $currency, string $order_id, string $secret_key): string {
        $data = [$amount, $currency, $order_id, $secret_key];
        return hash('sha256', implode(':', $data));
    }
    
    /**
     * Validate signature format
     */
    public static function validate_signature(string $signature): bool {
        return preg_match('/^[a-f0-9]{64}$/', $signature) === 1;
    }
}
```

#### 2.3 React-Native Payment Groups System
**Blocks-Optimized Architecture for Configurable Payment Groups:**

**Blocks-Native Payment Groups Manager:**
```php
<?php
declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

final class PayOp_Payment_Groups {
    /**
     * Get all configured payment groups formatted for React components
     */
    public function get_groups_for_blocks(): array {
        $groups = $this->get_groups();
        return array_map([$this, 'format_group_for_react'], $groups);
    }

    /**
     * Get groups available for current cart context (blocks-aware)
     */
    public function get_available_groups_for_blocks(array $cart_context): array {
        $available_groups = $this->filter_groups_by_context($cart_context);
        return $this->format_groups_for_react_display($available_groups);
    }

    /**
     * Create new payment group with React admin interface data
     */
    public function create_group_from_react(array $react_group_data): int {
        $group_data = $this->sanitize_react_group_data($react_group_data);
        return $this->create_group($group_data);
    }

    /**
     * Update group via React admin interface
     */
    public function update_group_from_react(int $group_id, array $react_group_data): bool {
        $group_data = $this->sanitize_react_group_data($react_group_data);
        return $this->update_group($group_id, $group_data);
    }

    /**
     * Get payment methods for specific group (React-optimized)
     */
    public function get_group_methods_for_blocks(int $group_id, array $cart_context = []): array {
        $methods = $this->get_group_methods($group_id, $cart_context);
        return $this->format_methods_for_react_display($methods);
    }

    /**
     * Real-time group filtering for React components
     */
    public function filter_groups_realtime(array $cart_data, array $customer_data): array {
        // Optimized for React component real-time updates
        return $this->apply_conditional_logic($cart_data, $customer_data);
    }

    // Core group management methods
    private function get_groups(): array { /* ... */ }
    private function create_group(array $group_data): int { /* ... */ }
    private function update_group(int $group_id, array $group_data): bool { /* ... */ }
    private function delete_group(int $group_id): bool { /* ... */ }

    // React-specific formatting methods
    private function format_group_for_react(array $group): array {
        // Format group data for React component consumption
    }

    private function sanitize_react_group_data(array $react_data): array {
        // Sanitize data received from React admin interface
    }
}
```

#### 2.4 Enhanced Payment Methods Management
**Dynamic Method Loading with Group Support:**
```php
class PayOp_Payment_Methods {
    /**
     * Fetch all 122 payment methods from PayOp API
     */
    public function fetch_methods(): array

    /**
     * Filter methods by supported currencies (EUR, USD, PHP, GBP, CAD, AUD, BRL, DKK)
     */
    public function filter_by_currency(string $currency): array

    /**
     * Filter methods by customer country (226+ supported countries)
     */
    public function filter_by_country(string $country): array

    /**
     * Group methods by admin-configured groups (NEW)
     */
    public function group_by_admin_config(): array

    /**
     * Get methods for specific payment group (NEW)
     */
    public function get_methods_for_group(int $group_id): array

    /**
     * Get required fields configuration for specific payment method
     */
    public function get_method_fields(int $method_id): array

    /**
     * Cache payment methods to reduce API calls
     */
    public function cache_methods(array $methods, int $expiry = 3600): void

    /**
     * Get cached methods if available and not expired
     */
    public function get_cached_methods(): ?array
}
```

### Phase 3: React Components & Additional Checkout Fields Integration (Week 3-4)

#### 3.1 Modern React Payment Method Registration
**Blocks-Native Client-Side Integration:**

**Primary Payment Method Registration:**
```javascript
import { registerPaymentMethod } from '@woocommerce/blocks-registry';
import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';

// Main PayOp payment method component
const PayOpPaymentMethod = () => {
    const [selectedGroup, setSelectedGroup] = useState(null);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const { eventRegistration, emitResponse } = usePaymentMethodInterface();
    const { onPaymentSetup } = eventRegistration;

    // Register payment processing handler
    useEffect(() => {
        const unsubscribe = onPaymentSetup(async () => {
            if (!selectedMethod) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: __('Please select a payment method', 'payop'),
                };
            }

            return {
                type: emitResponse.responseTypes.SUCCESS,
                meta: {
                    paymentMethodData: {
                        selectedGroup: selectedGroup?.id,
                        selectedMethod: selectedMethod?.id,
                        additionalFields: getAdditionalFieldsData(),
                    },
                },
            };
        });

        return unsubscribe;
    }, [selectedGroup, selectedMethod, onPaymentSetup, emitResponse]);

    return (
        <PayOpPaymentInterface
            selectedGroup={selectedGroup}
            selectedMethod={selectedMethod}
            onGroupSelect={setSelectedGroup}
            onMethodSelect={setSelectedMethod}
            isLoading={isLoading}
        />
    );
};

// Register the payment method
registerPaymentMethod({
    name: 'payop',
    label: <PayOpLabel />,
    content: <PayOpPaymentMethod />,
    edit: <PayOpPaymentMethod />,
    canMakePayment: ({ cart, cartTotals, billingAddress, shippingAddress }) => {
        // Dynamic availability based on cart/customer context
        return validatePaymentAvailability(cart, billingAddress);
    },
    supports: {
        features: ['products'],
        showSavedCards: false,
        showSaveOption: false,
    },
    ariaLabel: __('PayOp Payment Methods', 'payop'),
});
```

#### 3.2 Native WooCommerce Additional Checkout Fields Integration
**Blocks-Native Dynamic Fields Registration:**
```php
<?php
declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

final class PayOp_Additional_Fields {

    public function register_dynamic_fields(): void {
        add_action('woocommerce_init', [$this, 'register_payop_fields']);
    }

    public function register_payop_fields(): void {
        // Only register if blocks checkout is available
        if (!function_exists('woocommerce_register_additional_checkout_field')) {
            return;
        }

        $payment_methods = PayOp_Payment_Methods::get_all_methods_for_blocks();

        foreach ($payment_methods as $method) {
            $this->register_method_fields($method);
        }
    }

    private function register_method_fields(array $method): void {
        $required_fields = $method['config']['fields'] ?? [];

        foreach ($required_fields as $field) {
            // Skip standard WooCommerce fields
            if (in_array($field['name'], ['email', 'name', 'first_name', 'last_name'])) {
                continue;
            }

            woocommerce_register_additional_checkout_field([
                'id' => "payop/{$field['name']}",
                'label' => $field['title'] ?? $this->format_field_label($field['name']),
                'location' => 'order',
                'type' => $this->map_field_type($field['type']),
                'required' => $this->get_conditional_required_schema($field, $method),
                'hidden' => $this->get_conditional_hidden_schema($field, $method),
                'validation' => $this->get_field_validation_schema($field),
                'options' => $this->get_field_options($field),
                'attributes' => $this->get_field_attributes($field),
                'sanitize_callback' => [$this, 'sanitize_field_value'],
                'validate_callback' => [$this, 'validate_field_value'],
            ]);
        }
    }

    private function get_conditional_required_schema(array $field, array $method): array {
        return [
            'type' => 'object',
            'properties' => [
                'checkout' => [
                    'properties' => [
                        'payment_method' => ['const' => 'payop'],
                        'additional_fields' => [
                            'properties' => [
                                'payop/selected_method' => ['const' => (string)$method['identifier']]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    private function get_conditional_hidden_schema(array $field, array $method): array {
        return [
            'type' => 'object',
            'properties' => [
                'checkout' => [
                    'properties' => [
                        'payment_method' => ['not' => ['const' => 'payop']],
                        'additional_fields' => [
                            'properties' => [
                                'payop/selected_method' => ['not' => ['const' => (string)$method['identifier']]]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    private function get_field_validation_schema(array $field): array {
        $schema = ['type' => 'string'];

        if (isset($field['regexp'])) {
            $schema['pattern'] = $field['regexp'];
            $schema['errorMessage'] = sprintf(
                __('Please enter a valid %s', 'payop'),
                $field['title'] ?? $field['name']
            );
        }

        return $schema;
    }
}
```

#### 3.3 Enhanced Field Configuration System
**Based on API analysis of field requirements:**
- Email field: Required by all 122 methods
- Name field: Required by 120 methods
- Phone field: Required by 48 methods
- Document field: Required by 37 methods (with regional patterns)
- Banking fields: Required by 14 methods

**Field Manager Implementation:**
```php
class PayOp_Field_Manager {
    /**
     * Get required fields for specific payment method based on config.fields
     * Common patterns:
     * - All 122 methods: email (required)
     * - 120 methods: name (required) 
     * - 48 methods: phone (required)
     * - 37 methods: document (required, with regional patterns)
     * - 18 methods: date_of_birth (required, European banking)
     * - 14 methods: bank_code + bank_type (required)
     */
    public function get_required_fields(int $method_id): array
    
    /**
     * Validate field data against PayOp patterns
     */
    public function validate_field_data(array $data, array $field_config): bool
    
    /**
     * Apply regional validation patterns
     */
    public function apply_field_patterns(string $value, string $pattern): bool
    
    /**
     * Get field validation rules with error messages
     */
    public function get_field_validation_rules(array $field_config): array
    
    /**
     * Generate field labels with proper localization
     */
    public function get_field_label(string $field_name, array $field_config): string
    
    /**
     * Validate document patterns by region
     */
    public function validate_document_pattern(string $document, string $country): bool
    
    /**
     * Validate bank transfer specific fields
     */
    public function validate_bank_fields(array $bank_data, string $country): bool
}
```

#### 3.2 Regional Field Patterns
**Document Validation Patterns (37 methods require documents):**
- **Colombian/Latin American Documents**: `^\d{6,10}$` (6-10 digits) - 39 methods in Colombia
- **Uruguayan CI Documents**: `^\d{6,8}$` (6-8 digits)
- **Brazilian CPF/CNPJ**: `^\d{11,14}$` (11-14 digits) - 8 methods in Brazil

**Banking Patterns (14 methods require banking details):**
- **SEPA Transfers**: `^(SEPA|SEPA_INSTANT)$` - European countries
- **UK FPS Transfers**: `^(FPS)$` - United Kingdom
- **IBAN Classification**: `^(GB|NOT_GB)$` - IBAN routing

**Country Groupings for Field Requirements:**
- **European SEPA Zone**: Requires date_of_birth + bank_code + bank_type
- **Latin America**: Requires document + phone (high fraud prevention)
- **Asia-Pacific**: Minimal requirements (email + name typically sufficient)
- **North America**: Standard requirements (email + name)

**Field Complexity by Payment Type:**
- **Cards International (1 method)**: Simplest - email + name only
- **E-Wallets (10 methods)**: Simple - email + name only  
- **Cash Payments (42 methods)**: Medium - email + name + document (regional)
- **Bank Transfers (68 methods)**: Complex - email + name + banking details + sometimes date_of_birth
- **Crypto (1 method)**: Simple - email + name only

#### 3.3 Dynamic Form Generation
**Frontend Implementation:**
- JavaScript-based field injection
- Real-time validation
- Progressive enhancement
- Accessibility compliance

### Phase 4: Advanced React Components & Store API Extensions (Week 4-5)

#### 4.1 Advanced React Payment Interface
**Modern Block-Based Payment Selection:**
```javascript
import { useSelect, useDispatch } from '@wordpress/data';
import { useState, useEffect, useCallback } from '@wordpress/element';
import { __ } from '@wordpress/i18n';

const PayOpAdvancedInterface = () => {
    // Access WooCommerce Store data
    const { cart, billingAddress, shippingAddress, isCalculating } = useSelect(select => ({
        cart: select('wc/store/cart').getCartData(),
        billingAddress: select('wc/store/cart').getBillingAddress(),
        shippingAddress: select('wc/store/cart').getShippingAddress(),
        isCalculating: select('wc/store/cart').isCalculating(),
    }));

    const [paymentGroups, setPaymentGroups] = useState([]);
    const [selectedGroup, setSelectedGroup] = useState(null);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    // Real-time group filtering based on cart context
    const filterGroups = useCallback(async () => {
        if (!cart || isCalculating) return;

        setIsLoading(true);
        try {
            const context = {
                currency: cart.totals.currency_code,
                country: billingAddress?.country,
                cartTotal: cart.totals.total_price,
                items: cart.items,
                needsShipping: cart.needs_shipping,
            };

            const availableGroups = await fetchAvailableGroups(context);
            setPaymentGroups(availableGroups);

            // Auto-select first available group if none selected
            if (!selectedGroup && availableGroups.length > 0) {
                setSelectedGroup(availableGroups[0]);
            }
        } catch (error) {
            console.error('Error filtering payment groups:', error);
        } finally {
            setIsLoading(false);
        }
    }, [cart, billingAddress, isCalculating, selectedGroup]);

    useEffect(() => {
        filterGroups();
    }, [filterGroups]);

    return (
        <div className="payop-blocks-payment-interface">
            {isLoading ? (
                <PayOpLoadingSpinner />
            ) : (
                <>
                    <PaymentGroupSelector
                        groups={paymentGroups}
                        selectedGroup={selectedGroup}
                        onGroupSelect={setSelectedGroup}
                    />
                    {selectedGroup && (
                        <PaymentMethodGrid
                            group={selectedGroup}
                            selectedMethod={selectedMethod}
                            onMethodSelect={setSelectedMethod}
                            cartContext={cart}
                        />
                    )}
                    {selectedMethod && (
                        <AdditionalFieldsRenderer
                            method={selectedMethod}
                            billingAddress={billingAddress}
                        />
                    )}
                </>
            )}
        </div>
    );
};
```

#### 4.2 React-Based Admin Interface & Store API Extensions
**Modern Admin Interface for Group Management:**
```php
<?php
declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks\Admin;

final class PayOp_Admin_Interface {

    public function register_admin_pages(): void {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
    }

    public function add_admin_menu(): void {
        add_submenu_page(
            'woocommerce',
            __('PayOp Payment Groups', 'payop'),
            __('PayOp Groups', 'payop'),
            'manage_woocommerce',
            'payop-groups',
            [$this, 'render_admin_page']
        );
    }

    public function render_admin_page(): void {
        echo '<div id="payop-admin-interface"></div>';
    }

    public function enqueue_admin_assets(string $hook): void {
        if ('woocommerce_page_payop-groups' !== $hook) {
            return;
        }

        wp_enqueue_script(
            'payop-admin-interface',
            plugins_url('build/admin-interface.js', PAYOP_PLUGIN_FILE),
            ['wp-element', 'wp-components', 'wp-api-fetch', 'wp-i18n'],
            PAYOP_VERSION,
            true
        );

        wp_enqueue_style(
            'payop-admin-interface',
            plugins_url('build/admin-interface.css', PAYOP_PLUGIN_FILE),
            ['wp-components'],
            PAYOP_VERSION
        );

        wp_localize_script('payop-admin-interface', 'payopAdmin', [
            'apiUrl' => rest_url('payop/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'paymentMethods' => PayOp_Payment_Methods::get_all_methods_for_admin(),
            'currencies' => $this->get_supported_currencies(),
            'countries' => $this->get_supported_countries(),
        ]);
    }
}

/**
 * Store API Extensions for Admin Interface
 */
final class PayOp_Store_API_Extensions {

    public function register_endpoints(): void {
        add_action('rest_api_init', [$this, 'register_rest_routes']);
    }

    public function register_rest_routes(): void {
        register_rest_route('payop/v1', '/groups', [
            'methods' => 'GET',
            'callback' => [$this, 'get_groups'],
            'permission_callback' => [$this, 'check_admin_permissions'],
        ]);

        register_rest_route('payop/v1', '/groups', [
            'methods' => 'POST',
            'callback' => [$this, 'create_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
            'args' => $this->get_group_schema(),
        ]);

        register_rest_route('payop/v1', '/groups/(?P<id>\d+)', [
            'methods' => 'PUT',
            'callback' => [$this, 'update_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
            'args' => $this->get_group_schema(),
        ]);

        register_rest_route('payop/v1', '/groups/(?P<id>\d+)', [
            'methods' => 'DELETE',
            'callback' => [$this, 'delete_group'],
            'permission_callback' => [$this, 'check_admin_permissions'],
        ]);
    }

    private function get_group_schema(): array {
        return [
            'name' => [
                'type' => 'string',
                'required' => true,
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'description' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
            'display_order' => [
                'type' => 'integer',
                'minimum' => 0,
            ],
            'conditions' => [
                'type' => 'object',
                'properties' => [
                    'currencies' => ['type' => 'array'],
                    'countries' => ['type' => 'array'],
                    'min_amount' => ['type' => 'number'],
                    'max_amount' => ['type' => 'number'],
                ],
            ],
            'methods' => [
                'type' => 'array',
                'items' => ['type' => 'integer'],
            ],
        ];
    }
}
```

#### 4.3 Direct Integration Payment Flow Implementation
**Based on PayOp Direct Integration (NOT using PayOp hosted checkout):**

**Step-by-Step Flow:**
1. **Initial Email Collection**: Collect email address only to minimize checkout friction
2. **Payment Method Fetching**: Call `GET /v1/instrument-settings/payment-methods/available-for-application/{ID}` to get 122 available methods
3. **Method Filtering**: Filter by store currency and customer country from 226+ supported countries
4. **Payment Method Selection**: Display filtered methods grouped by type or region
5. **Dynamic Field Rendering**: Based on selected method's `config.fields`, render additional required fields:
   - **Simple Methods**: Email + name only (cards_international, crypto, most e-wallets)
   - **Medium Complexity**: Email + name + phone (some cash payments)
   - **Complex Methods**: Email + name + phone + document + banking details (Latin American PSE, European SEPA)
6. **Real-time Validation**: Apply field patterns and validation rules as user types
7. **Order Placement**: User clicks "Place Order" with all required data collected
8. **Invoice Creation**: Call `POST /v1/invoices/create` with proper SHA-256 signature
9. **Checkout Transaction**: Call `POST /v1/checkout/create` with collected customer data and selected payment method
10. **Status Polling**: Use `GET /v1/checkout/check-invoice-status/{invoiceID}` to get redirect information
11. **Direct Redirect**: Extract `form.url` from status response and redirect customer directly to payment provider
12. **IPN Processing**: Handle payment notifications from PayOp's documented IP addresses

**Critical Implementation Note**: The plugin completely bypasses PayOp's hosted checkout page. The `check-invoice-status` endpoint returns the payment provider's URL for direct redirection.

### Phase 5: React Admin Components & Advanced Group Builder (Week 5-6)

#### 5.1 Modern React-Based Admin Interface
**Blocks-Native Admin Configuration:**
- API credentials management with modern security patterns
- **React-based dynamic payment group configuration**
- **Blocks-exclusive settings (no legacy options)**
- Payment method enable/disable with real-time preview
- **Advanced grouping with drag-and-drop interface**
- **Conditional display rules with visual builder**
- Modern logging and debugging with Store API integration
- Invoice expiration configuration optimized for blocks checkout

#### 5.2 Advanced React Group Builder
**Modern Admin Features:**
```javascript
// React Admin Group Builder Component
import { useState, useEffect } from '@wordpress/element';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Button, Panel, PanelBody, TextControl, SelectControl } from '@wordpress/components';

const PayOpGroupBuilder = () => {
    const [groups, setGroups] = useState([]);
    const [availableMethods, setAvailableMethods] = useState([]);
    const [selectedGroup, setSelectedGroup] = useState(null);
    const [previewMode, setPreviewMode] = useState(false);

    useEffect(() => {
        loadGroupsAndMethods();
    }, []);

    const handleDragEnd = (result) => {
        if (!result.destination) return;

        const { source, destination } = result;

        if (source.droppableId === 'available-methods' && destination.droppableId.startsWith('group-')) {
            // Add method to group
            const groupId = destination.droppableId.replace('group-', '');
            addMethodToGroup(result.draggableId, groupId, destination.index);
        } else if (source.droppableId.startsWith('group-') && destination.droppableId === 'available-methods') {
            // Remove method from group
            const groupId = source.droppableId.replace('group-', '');
            removeMethodFromGroup(result.draggableId, groupId);
        }
    };

    return (
        <div className="payop-group-builder">
            <div className="builder-header">
                <h2>{__('Payment Group Builder', 'payop')}</h2>
                <Button
                    isPrimary
                    onClick={() => setPreviewMode(!previewMode)}
                >
                    {previewMode ? __('Edit Mode', 'payop') : __('Preview Mode', 'payop')}
                </Button>
            </div>

            {previewMode ? (
                <GroupPreview groups={groups} />
            ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                    <div className="builder-content">
                        <div className="available-methods">
                            <h3>{__('Available Payment Methods', 'payop')}</h3>
                            <Droppable droppableId="available-methods">
                                {(provided) => (
                                    <div {...provided.droppableProps} ref={provided.innerRef}>
                                        {availableMethods.map((method, index) => (
                                            <PaymentMethodCard
                                                key={method.id}
                                                method={method}
                                                index={index}
                                            />
                                        ))}
                                        {provided.placeholder}
                                    </div>
                                )}
                            </Droppable>
                        </div>

                        <div className="payment-groups">
                            <h3>{__('Payment Groups', 'payop')}</h3>
                            {groups.map((group) => (
                                <PaymentGroupEditor
                                    key={group.id}
                                    group={group}
                                    onUpdate={updateGroup}
                                    onDelete={deleteGroup}
                                />
                            ))}
                            <Button isSecondary onClick={createNewGroup}>
                                {__('Add New Group', 'payop')}
                            </Button>
                        </div>
                    </div>
                </DragDropContext>
            )}
        </div>
    );
};
```

#### 5.3 Order Management Integration
**HPOS Compatibility:**
- Custom order meta handling for PayOp transaction data
- Transaction status tracking with invoice and transaction IDs
- Order notes integration for payment status updates
- Support for WooCommerce order status mapping:
  - PayOp Invoice Status 0 (New) → WooCommerce Pending
  - PayOp Invoice Status 1 (Paid) → WooCommerce Processing/Completed  
  - PayOp Invoice Status 2 (Overdue) → WooCommerce Cancelled
  - PayOp Invoice Status 4 (Pending) → WooCommerce On Hold
  - PayOp Invoice Status 5 (Failed) → WooCommerce Failed

**Note**: Refunds and withdrawals are handled manually outside the plugin - no automated refund processing implemented.

### Phase 6: Security & Error Handling (Week 6-7)

#### 6.1 Security Implementation
**Security Measures:**
- Input sanitization and validation
- SQL injection prevention
- XSS protection
- CSRF token verification
- Secure API communication

#### 6.2 Error Handling System
**Based on PayOp API error codes and documented responses:**
- **401 Unauthorized**: JWT token expired or invalid - implement automatic token refresh logic
- **403 Forbidden**: Permission/access denied - log and display user-friendly message
- **404 Not Found**: Invoice/transaction not found - validate IDs and retry mechanism
- **422 Unprocessable Entity**: Field validation errors or payment method disabled - display specific field errors
- **500 Internal Server Error**: PayOp server issues - implement exponential backoff retry

**Invoice Status Error Handling:**
- **Status 0 (New)**: Invoice created but no payment initiated
- **Status 1 (Paid)**: Payment successful - complete order
- **Status 2 (Overdue)**: Invoice expired after 24 hours - create new invoice
- **Status 4 (Pending)**: Payment in progress - continue polling
- **Status 5 (Failed)**: Payment failed - display error and allow retry

**Transaction State Error Handling:**
- **State 1 (New)**: No action taken yet
- **State 2 (Accepted)**: Payment successful 
- **State 3 (Failed)**: Technical/financial failure
- **State 4 (Pending)**: Awaiting payment
- **State 5 (Failed)**: Alternative failure state
- **State 9 (Pre-approved)**: Submitted, awaiting funds
- **State 15 (Timeout)**: Lack of final confirmation

#### 6.3 Logging and Monitoring
**Comprehensive Logging:**
- API request/response logging
- Error tracking and reporting
- Performance monitoring
- User action auditing

### Phase 7: IPN & Webhook Handling (Week 7-8)

#### 7.1 IPN Endpoint Implementation
**Webhook Processing:**
```php
class PayOp_IPN_Handler {
    public function handle_ipn_request()
    public function validate_ipn_source(string $ip): bool
    public function process_payment_notification(array $data): bool
    public function update_order_status(int $order_id, string $status): void
}
```

#### 7.2 IP Whitelisting and Security
**Security Implementation:**
- **PayOp IP Validation**: Verify IPN requests come from documented IP addresses:
  - *************
  - *************  
  - ************
  - *************
- **Request signature verification**: Validate IPN payload integrity
- **Duplicate notification handling**: Store processed IPN IDs to prevent duplicate processing
- **Status change processing**: Only update order status if new status differs from current

**IPN Payload Structure (documented format):**
```php
{
  "invoice": {
    "id": "invoice_uuid",
    "status": 1, // Invoice status code
    "txid": "transaction_uuid",
    "metadata": {
      "orderId": "wc_order_id",
      "amount": 10.00,
      "customerId": 12345
    }
  },
  "transaction": {
    "id": "transaction_uuid", 
    "state": 2, // Transaction state code
    "order": {
      "id": "order_identifier"
    },
    "error": {
      "message": "Error description",
      "code": "error_code"
    }
  }
}
```

### Phase 8: Testing & Quality Assurance (Week 8-9)

#### 8.1 Unit Testing
**Test Coverage:**
- API client functionality
- Signature generation
- Field validation
- Payment method filtering
- Error handling scenarios

#### 8.2 Integration Testing
**End-to-End Testing Scenarios:**
- **Payment Method Coverage**: Test representative methods from each type:
  - Cards International (ID: 700001) - Global coverage, simple fields
  - European SEPA (ID: 200031) - Complex banking fields with date_of_birth
  - Colombian PSE (ID: 634) - Document validation with regex patterns  
  - Philippines E-Wallet (ID: 862) - Regional e-wallet, simple fields
  - Cash Payment (ID: 705) - Document + phone requirements
  - Crypto Payment (ID: 6110) - Global crypto, simple fields
- **Currency Testing**: Test all 8 supported currencies (EUR, USD, PHP, GBP, CAD, AUD, BRL, DKK)
- **Geographic Testing**: Test major country markets:
  - Europe: Germany, France, Austria (SEPA methods)
  - Latin America: Colombia, Peru, Mexico (document-heavy methods)  
  - Asia-Pacific: Philippines (e-wallet methods)
  - North America: USA, Canada (card methods)
- **Field Validation Testing**: Test all documented regex patterns and validation rules
- **Error Scenarios**: Test all HTTP error codes and transaction states
- **IPN Processing**: Test with actual PayOp IPN payloads from documented IP addresses

**Performance Testing Targets:**
- Payment method API call: < 2 seconds (122 methods, ~54KB response)
- Invoice creation: < 1 second
- Checkout transaction: < 2 seconds  
- Status polling: < 1 second per poll

#### 8.3 Performance Testing
**Optimization Areas:**
- API response caching
- Database query optimization
- Frontend asset loading
- Memory usage monitoring

### Phase 9: Documentation & Deployment (Week 9-10)

#### 9.1 Technical Documentation
**Documentation Deliverables:**
- Installation guide with PayOp account setup requirements
- Configuration manual for all 122 payment methods
- API integration details with signature generation examples
- Field validation patterns and regional requirements
- Troubleshooting guide for common error scenarios
- Developer documentation with code examples

#### 9.2 User Documentation
**End-User Guides:**
- Admin setup instructions with PayOp credential configuration
- Payment method configuration and grouping strategies
- Order management procedures with HPOS support
- Customer support guidelines for payment issues
- Regional compliance considerations

#### 9.3 Deployment Preparation
**Release Checklist:**
- Code review and optimization for 122 payment method support
- Security audit focusing on signature generation and IPN handling
- Performance validation with large payment method datasets
- WordPress.org compliance check
- Version control and tagging

**Production Deployment Considerations:**
- PayOp has no sandbox environment - all testing occurs on production API
- Implement feature flags for gradual payment method rollout
- Monitor API rate limits and caching effectiveness
- Set up comprehensive logging for production troubleshooting

## Technical Specifications

### Blocks-Optimized Database Schema
**Custom Tables Optimized for React Components and Store API:**
```sql
-- Payment method cache optimized for blocks rendering
CREATE TABLE {prefix}_payop_payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_id INT NOT NULL,
    method_type ENUM('bank_transfer', 'cash', 'ewallet', 'cards_international', 'crypto') NOT NULL,
    method_title VARCHAR(255) NOT NULL,
    method_logo_url TEXT,
    currencies JSON NOT NULL,
    countries JSON NOT NULL,
    config_fields JSON NOT NULL,
    blocks_metadata JSON, -- React component specific data
    is_enabled TINYINT(1) DEFAULT 1,
    last_updated DATETIME NOT NULL,
    cache_expires DATETIME NOT NULL,
    INDEX idx_method_id (method_id),
    INDEX idx_method_type (method_type),
    INDEX idx_enabled (is_enabled),
    INDEX idx_cache_expires (cache_expires),
    INDEX idx_blocks_enabled (is_enabled, cache_expires) -- Optimized for blocks queries
);

-- Payment groups optimized for React admin interface
CREATE TABLE {prefix}_payop_payment_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    display_order INT DEFAULT 0,
    is_enabled TINYINT(1) DEFAULT 1,
    conditions JSON, -- Currency, country, amount conditions
    react_config JSON, -- React component configuration
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    INDEX idx_enabled (is_enabled),
    INDEX idx_display_order (display_order),
    INDEX idx_blocks_query (is_enabled, display_order) -- Optimized for blocks rendering
);

-- Payment method to group assignments with blocks optimization
CREATE TABLE {prefix}_payop_group_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    method_id INT NOT NULL,
    display_order INT DEFAULT 0,
    is_enabled TINYINT(1) DEFAULT 1,
    blocks_config JSON, -- React-specific configuration per assignment
    FOREIGN KEY (group_id) REFERENCES {prefix}_payop_payment_groups(id) ON DELETE CASCADE,
    INDEX idx_group_id (group_id),
    INDEX idx_method_id (method_id),
    INDEX idx_enabled (is_enabled),
    INDEX idx_blocks_render (group_id, is_enabled, display_order), -- Optimized for React rendering
    UNIQUE KEY unique_group_method (group_id, method_id)
);

-- Store API cache for blocks performance
CREATE TABLE {prefix}_payop_blocks_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cache_key VARCHAR(255) NOT NULL,
    cache_data JSON NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    UNIQUE KEY unique_cache_key (cache_key),
    INDEX idx_expires (expires_at)
);

-- Enhanced transaction logs with PayOp-specific data
CREATE TABLE {prefix}_payop_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    invoice_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255),
    payment_method_id INT NOT NULL,
    payment_method_type VARCHAR(50) NOT NULL,
    invoice_status TINYINT NOT NULL DEFAULT 0,
    transaction_state TINYINT NOT NULL DEFAULT 1,
    customer_data JSON,
    payop_response JSON,
    redirect_url TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    INDEX idx_order_id (order_id),
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_invoice_status (invoice_status),
    INDEX idx_transaction_state (transaction_state),
    INDEX idx_expires_at (expires_at)
);

-- IPN processing log for debugging and compliance
CREATE TABLE {prefix}_payop_ipn_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255),
    source_ip VARCHAR(45) NOT NULL,
    payload JSON NOT NULL,
    processed_at DATETIME NOT NULL,
    processing_result ENUM('success', 'duplicate', 'invalid_ip', 'invalid_data', 'error') NOT NULL,
    error_message TEXT,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_source_ip (source_ip),
    INDEX idx_processed_at (processed_at),
    INDEX idx_result (processing_result)
);
```

### Blocks-Native Configuration Constants
**Optimized for WooCommerce Blocks architecture exclusively:**

```php
<?php
declare(strict_types=1);

namespace PayOp\WooCommerce\Blocks;

final class PayOp_Constants {
    // WooCommerce Version Requirements
    public const MIN_WC_VERSION = '8.3.0';
    public const MIN_WP_VERSION = '6.4.0';
    public const MIN_PHP_VERSION = '8.2.0';

    // PayOp API Configuration
    public const API_BASE_URL = 'https://api.payop.com';
    public const API_VERSION_V1 = '/v1';
    public const API_VERSION_V2 = '/v2';

    // Documented API Endpoints
    public const ENDPOINT_PAYMENT_METHODS = '/v1/instrument-settings/payment-methods/available-for-application';
    public const ENDPOINT_INVOICE_CREATE = '/v1/invoices/create';
    public const ENDPOINT_INVOICE_GET = '/v1/invoices';
    public const ENDPOINT_CHECKOUT_CREATE = '/v1/checkout/create';
    public const ENDPOINT_CHECKOUT_STATUS = '/v1/checkout/check-invoice-status';
    public const ENDPOINT_TRANSACTION_GET = '/v2/transactions';
    public const ENDPOINT_CHECKOUT_VOID = '/v1/checkout/void';

    // IPN Security - From documentation
    public const IPN_ALLOWED_IPS = [
        '*************',
        '*************',
        '************',
        '*************'
    ];

    // Invoice Status Codes
    public const INVOICE_STATUS_NEW = 0;
    public const INVOICE_STATUS_PAID = 1;
    public const INVOICE_STATUS_OVERDUE = 2;
    public const INVOICE_STATUS_PENDING = 4;
    public const INVOICE_STATUS_FAILED = 5;

    // Transaction State Codes
    public const TRANSACTION_STATE_NEW = 1;
    public const TRANSACTION_STATE_ACCEPTED = 2;
    public const TRANSACTION_STATE_FAILED = 3;
    public const TRANSACTION_STATE_PENDING = 4;
    public const TRANSACTION_STATE_TIMEOUT = 15;

    // Blocks-Exclusive Constants
    public const BLOCK_PAYMENT_METHOD_NAME = 'payop';
    public const BLOCK_SCRIPT_HANDLE = 'payop-blocks-payment-method';
    public const BLOCK_ADMIN_SCRIPT_HANDLE = 'payop-admin-interface';
    public const BLOCK_STYLE_HANDLE = 'payop-blocks-style';

    // React Component Identifiers
    public const REACT_PAYMENT_COMPONENT = 'PayOpPaymentMethod';
    public const REACT_ADMIN_COMPONENT = 'PayOpAdminInterface';
    public const REACT_GROUP_BUILDER = 'PayOpGroupBuilder';

    // Store API Endpoints
    public const STORE_API_NAMESPACE = 'payop/v1';
    public const STORE_API_GROUPS_ENDPOINT = '/groups';
    public const STORE_API_METHODS_ENDPOINT = '/methods';
    public const STORE_API_CHECKOUT_ENDPOINT = '/checkout';

    // Default Payment Groups (React-optimized)
    public const DEFAULT_GROUPS = [
        'cards' => [
            'name' => 'Credit & Debit Cards',
            'icon' => 'credit-card',
            'order' => 1,
        ],
        'banking' => [
            'name' => 'Bank Transfers',
            'icon' => 'bank',
            'order' => 2,
        ],
        'wallets' => [
            'name' => 'Digital Wallets',
            'icon' => 'wallet',
            'order' => 3,
        ],
        'cash' => [
            'name' => 'Cash Payments',
            'icon' => 'money',
            'order' => 4,
        ],
        'crypto' => [
            'name' => 'Cryptocurrency',
            'icon' => 'bitcoin',
            'order' => 5,
        ],
    ];

    // Cache Configuration
    public const CACHE_TTL_PAYMENT_METHODS = 3600; // 1 hour
    public const CACHE_TTL_GROUPS = 1800; // 30 minutes
    public const CACHE_TTL_BLOCKS_DATA = 300; // 5 minutes
}
```

## Risk Mitigation

### Technical Risks (Blocks-Native Focus)
1. **API Rate Limiting**: Implement aggressive caching for 122 payment methods with React-optimized data structures
2. **Payment Method Changes**: Real-time API sync with Store API integration and React state management
3. **Field Validation Complexity**: JSON Schema-based validation for 37 different document patterns using WooCommerce additional fields
4. **React Performance**: Optimize React component rendering for 122+ payment methods with virtualization
5. **No Sandbox Environment**: PayOp lacks sandbox - requires careful production testing with comprehensive logging
6. **WooCommerce Version Dependency**: Strict dependency on WooCommerce 8.3+ - no fallback for older versions
7. **Store API Limitations**: Potential limitations in Store API for complex payment method data
8. **React Build Complexity**: Managing React build process and ensuring compatibility with WordPress block editor

### Business Risks  
1. **Compliance Requirements**: Regional document validation for 226+ countries with specific patterns
2. **Currency Fluctuations**: Real-time rate handling for 8 supported currencies
3. **Regional Regulations**: Flexible field configuration for varying KYC requirements by country
4. **User Experience**: Extensive testing across 122 payment methods with different field requirements

### PayOp-Specific Implementation Challenges (Blocks-Native)
1. **React Dynamic Fields**: Handle varying field requirements (2-6+ fields) using WooCommerce additional checkout fields with React components
2. **JSON Schema Validation**: Implement 37 different document patterns using WooCommerce's native validation system
3. **Store API Status Polling**: Efficient polling strategy integrated with WooCommerce Store API without overwhelming PayOp API
4. **React State Management**: Complex state management for payment method selection, group filtering, and dynamic field rendering
5. **IPN Security**: Validate IPNs from 4 specific IP addresses with Store API security patterns
6. **Real-time Group Filtering**: Efficiently filter 122 payment methods using React hooks and WooCommerce Store data
7. **React Performance**: Optimize rendering of large payment method datasets with virtualization and memoization
8. **Build Process Complexity**: Manage React build pipeline compatible with WordPress plugin deployment

## Success Metrics

### Technical KPIs
- API response time < 2 seconds (critical for 122 payment methods loading)
- Payment success rate > 95% across all method types
- Error rate < 1% for signature generation and field validation
- Page load impact < 500ms with lazy-loaded payment methods
- Cache hit rate > 80% for payment methods API calls

### Business KPIs  
- **Payment Method Adoption**: Track usage across 5 payment types
- **Geographic Coverage**: Monitor adoption across 226+ supported countries
- **Currency Distribution**: Track transactions across 8 supported currencies
- **Conversion Rate**: Measure checkout completion by payment method complexity
- **Regional Performance**: Compare simple vs complex field requirement conversion rates
- **Customer Satisfaction**: Monitor support tickets related to payment method confusion
- **Admin Efficiency**: Reduce payment method configuration time

### PayOp Integration Specific Metrics
- **Field Validation Accuracy**: < 0.1% false rejection rate for regional patterns
- **IPN Processing Reliability**: 100% successful processing of valid IPNs
- **Payment Method Cache Effectiveness**: Reduce API calls by 80% through caching
- **Status Polling Efficiency**: Average 2-3 polls per transaction completion
- **Document Validation Success**: > 95% accuracy for regional document patterns
- **Block-Based Checkout Adoption**: Track usage of block vs. legacy checkout
- **Dynamic Group Performance**: < 500ms for group filtering and method loading
- **Admin Interface Efficiency**: Reduce group configuration time by 70%

## Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| 1 | 2 weeks | Blocks-Native Foundation & Architecture |
| 2 | 1 week | Store API Integration & React Payment Groups |
| 3 | 1 week | React Components & Additional Checkout Fields |
| 4 | 1 week | Advanced React Components & Store API Extensions |
| 5 | 1 week | React Admin Components & Advanced Group Builder |
| 6 | 1 week | Security & Error Handling (Blocks-Focused) |
| 7 | 1 week | IPN & Store API Webhook Handling |
| 8 | 1 week | Testing & QA (Blocks-Exclusive) |
| 9 | 1 week | Documentation & Deployment |

**Total Development Time**: 10 weeks

## Next Steps

1. **Development Environment Setup**: Configure Local by Flywheel development environment
2. **PayOp Project Access**: Obtain PayOp production credentials (Public Key, Secret Key, JWT Token) - Note: PayOp has no sandbox environment
3. **Repository Setup**: Initialize version control and project structure  
4. **Phase 1 Kickoff**: Begin foundation development with core plugin structure
5. **API Integration Testing**: Test with PayOp production API using documented test credentials pattern

**Important Note**: Since PayOp does not provide a sandbox environment, initial development and testing must be conducted carefully with production API endpoints using small transaction amounts or test payment methods where available.

This comprehensive development plan is based on systematic analysis of PayOp's actual API documentation and payment method data. The plan addresses the complexity of integrating 122 payment methods across 226+ countries with varying field requirements, from simple 2-field methods to complex 6+ field banking integrations.

**Key Technical Achievements (Blocks-Native):**
- **Blocks-Exclusive Architecture**: Built exclusively for WooCommerce Blocks (8.3+) with no legacy dependencies
- **React-Based Frontend**: Modern React components for payment method selection and dynamic field rendering
- **Store API Integration**: Full integration with WooCommerce Store API for seamless blocks checkout
- **Dynamic Payment Groups**: React-based admin-configurable grouping system with visual interface
- **Advanced React Admin**: Drag-and-drop group builder with real-time preview capabilities
- **Native Additional Fields**: Full integration with WooCommerce's additional checkout fields API
- **JSON Schema Validation**: Modern validation patterns for 37 different document types
- **Direct Integration**: PayOp API integration bypassing hosted checkout completely
- **Performance Optimized**: React virtualization and memoization for handling 122+ payment methods
- **Modern Security**: Store API security patterns with proper authentication and validation
- **Production-Ready**: No sandbox dependency with comprehensive logging and error handling
- **Simplified Scope**: No refund/withdrawal automation (handled manually), one-time payments only

**Implementation Priorities (Blocks-Native):**
1. **Phase 1-2 (Weeks 1-3)**: Blocks-native foundation and React payment groups - Critical for WooCommerce 8.3+ compatibility
2. **Phase 3-4 (Weeks 4-5)**: Advanced React components and Store API integration - Core user experience and management
3. **Phase 5-7 (Weeks 6-8)**: React admin interface, security, and Store API webhooks - Production readiness
4. **Phase 8-9 (Weeks 9-10)**: Comprehensive testing and documentation - Quality assurance and deployment

The systematic approach ensures robust handling of PayOp's diverse payment ecosystem while focusing exclusively on modern WooCommerce block-based checkout, providing merchants with a scalable, secure, and future-proof payment solution built for the modern WooCommerce ecosystem.

## WooCommerce Blocks-Native Architecture Summary

This completely modernized development plan eliminates all legacy dependencies and focuses exclusively on WooCommerce block-based checkout (WooCommerce 8.3+). Key architectural decisions include:

### Blocks-Exclusive Integration
- **No Legacy Support**: Complete removal of `WC_Payment_Gateway` and shortcode-based checkout
- **React-First Design**: All frontend components built with React using WooCommerce Blocks patterns
- **Store API Native**: Full integration with WooCommerce Store API for all data operations
- **Modern Validation**: JSON Schema-based validation using WooCommerce's additional checkout fields

### Advanced React Architecture
- **Component-Based**: Modular React components for payment groups, method selection, and dynamic fields
- **State Management**: Modern React hooks and context for complex payment flow state
- **Performance Optimized**: Virtualization and memoization for handling large payment method datasets
- **Real-time Updates**: Live filtering and validation based on cart and customer context

### Modern Admin Experience
- **React Admin Interface**: Drag-and-drop group builder with real-time preview
- **Store API Integration**: RESTful API endpoints for all admin operations
- **Visual Configuration**: Interactive payment method management with immediate feedback
- **Performance Analytics**: Built-in tracking and optimization recommendations

### Future-Proof Foundation
- **WooCommerce 8.3+ Exclusive**: Built for the current and future WooCommerce ecosystem
- **Modern Security**: Store API security patterns with proper authentication
- **Scalable Architecture**: Designed to handle PayOp's 122+ payment methods efficiently
- **Extensible Design**: Plugin architecture ready for future WooCommerce enhancements

This blocks-native approach ensures the PayOp plugin is built for the modern WooCommerce ecosystem, providing merchants with a cutting-edge payment solution that leverages the latest WooCommerce capabilities without any legacy baggage.
