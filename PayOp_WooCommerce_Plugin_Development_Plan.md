# PayOp WooCommerce Plugin Development Plan

## Project Overview

Develop a modern WooCommerce payment gateway plugin using PayOp's **Direct Integration API** with 122 payment methods across 226+ countries. The plugin implements **WooCommerce block-based checkout** (default since WooCommerce 8.3) with dynamic payment groups, configurable admin interface, and **direct payment provider redirection**, completely bypassing PayOp's hosted checkout page.

**Key Integration Requirements**:
- **Block-Based Checkout First**: Primary focus on WooCommerce Blocks (default since November 2023)
- **Dynamic Payment Groups**: Configurable payment method grouping with admin interface
- **Direct Integration Only**: Skip PayOp's hosted checkout entirely and redirect customers directly to payment providers
- **Modern WooCommerce Compatibility**: Support both legacy shortcode and block-based checkout systems

**Excluded Functionality**:
- PayOp hosted checkout page (completely bypassed)
- Automated refund processing (handled manually)
- Withdrawal functionality (handled manually)
- Subscription payments (PayOp supports one-time payments only)

## Technical Foundation

### Core Requirements
- **PHP**: 8.2+ with modern OOP patterns
- **WordPress**: Latest stable with HPOS support
- **WooCommerce**: Latest with block-based checkout (8.3+)
- **WooCommerce Blocks**: Primary integration target (default since November 2023)
- **PayOp API**: Direct Integration (122 methods analyzed)
- **Architecture**: Modular, scalable, secure, block-first design

### Block-Based Checkout Integration (WooCommerce 8.3+ Default)
**Critical Requirement**: This plugin prioritizes WooCommerce block-based checkout integration while maintaining legacy shortcode compatibility. The implementation includes:

**Block-Based Checkout Flow:**
1. **Payment Method Registration**: Register via `registerPaymentMethod()` with React components
2. **Server-Side Integration**: Extend `AbstractPaymentMethodType` for block compatibility
3. **Dynamic Payment Groups**: Admin-configurable grouping of 122 payment methods
4. **Dynamic Field Collection**: Use WooCommerce's additional checkout fields API for payment-specific data
5. **Real-time Validation**: JSON Schema-based conditional field validation
6. **Direct Integration**: PayOp API integration bypassing hosted checkout

**PayOp Direct Integration Flow:**
1. **Invoice Creation**: Use `POST /v1/invoices/create` with proper signature
2. **Dynamic Field Collection**: Collect payment method-specific fields based on `config.fields` from API
3. **Checkout Creation**: Use `POST /v1/checkout/create` to get direct payment provider URL
4. **Direct Redirect**: Redirect customer directly to payment provider (NOT to PayOp checkout)
5. **IPN Handling**: Process payment notifications from documented IP addresses

### API Integration Specifications
Based on factual API documentation analysis:
- **Payment Methods**: 122 total (68 bank_transfer, 42 cash, 10 ewallet, 1 cards_international, 1 crypto)
- **Geographic Coverage**: 226+ countries
- **Currency Support**: 8 currencies (EUR: 92 methods, USD: 18 methods, PHP: 10 methods, etc.)
- **Field Requirements**: Dynamic (email: 122, name: 120, phone: 48, document: 37, etc.)
- **API Base URL**: `https://api.payop.com` (from documented endpoints)
- **Payment Types**: One-time payments only (no subscriptions)

### Dynamic Payment Groups Architecture
**New Requirement**: Replace hard-coded payment groups with dynamic, admin-configurable system:
- **Admin Interface**: Visual payment method management with drag-and-drop grouping
- **Group Configuration**: Custom group names, descriptions, and display order
- **Method Assignment**: Flexible assignment of payment methods to multiple groups
- **Conditional Display**: Show/hide groups based on currency, country, cart contents
- **Performance Optimization**: Efficient caching and lazy loading of payment method data

## Development Phases

### Phase 1: Foundation & Block-Based Architecture (Week 1-2)

#### 1.1 Plugin Architecture Setup
**Deliverables:**
- Main plugin file with proper headers
- Autoloader implementation
- Namespace structure (`PayOp\WooCommerce\`)
- Constants definition (API endpoints, version, etc.)
- **Block-based checkout integration foundation**

**Enhanced File Structure:**
```
payop-direct-payment-woo/
├── payop-woocommerce-gateway.php (main file)
├── includes/
│   ├── class-payop-gateway.php (legacy shortcode support)
│   ├── class-payop-blocks-integration.php (NEW: blocks integration)
│   ├── class-payop-payment-method-type.php (NEW: extends AbstractPaymentMethodType)
│   ├── class-payop-api-client.php
│   ├── class-payop-payment-methods.php
│   ├── class-payop-payment-groups.php (NEW: dynamic groups)
│   ├── class-payop-field-manager.php
│   ├── class-payop-signature-generator.php
│   └── admin/
│       ├── class-payop-admin-settings.php
│       ├── class-payop-payment-method-manager.php
│       └── class-payop-group-manager.php (NEW: group admin interface)
├── assets/
│   ├── js/
│   │   ├── blocks/ (NEW: block-specific JS)
│   │   │   ├── payment-method.js
│   │   │   └── payment-groups.js
│   │   └── legacy/ (legacy checkout JS)
│   ├── css/
│   └── images/
├── build/ (NEW: compiled block assets)
└── templates/
    └── checkout/
```

#### 1.2 Configuration Management
**Implementation:**
- Secure credential storage (Public Key, Secret Key, JWT Token)
- API endpoint configuration
- Logging system setup
- Payment method caching configuration
- **Dynamic payment group configuration storage**
- **Block-based checkout settings**

**Security Considerations:**
- Encrypt sensitive data in database
- Implement proper nonce verification
- Sanitize all inputs
- Validate API responses
- **Secure group configuration data**

#### 1.3 WooCommerce Gateway Registration
**Dual Integration Approach:**

**Legacy Gateway Class (Shortcode Compatibility):**
```php
class PayOp_Gateway extends WC_Payment_Gateway {
    public function __construct() {
        $this->id = 'payop';
        $this->method_title = 'PayOp Payment Gateway';
        $this->method_description = 'Accept payments via PayOp with 122+ payment methods';
        $this->supports = [
            'products'
            // Note: Only one-time payments supported, no subscriptions
            // Note: Refunds and withdrawals handled manually - no automated processing
        ];
    }
}
```

**Block-Based Integration Class (Primary):**
```php
class PayOp_Payment_Method_Type extends AbstractPaymentMethodType {
    protected $name = 'payop';

    public function initialize() {
        $this->settings = get_option('woocommerce_payop_settings', []);
    }

    public function is_active(): bool {
        return filter_var($this->get_setting('enabled', false), FILTER_VALIDATE_BOOLEAN);
    }

    public function get_payment_method_script_handles(): array {
        // Register block-specific scripts
        return ['payop-blocks-integration'];
    }

    public function get_payment_method_data(): array {
        return [
            'title' => $this->get_setting('title'),
            'description' => $this->get_setting('description'),
            'supports' => $this->get_supported_features(),
            'paymentGroups' => $this->get_payment_groups(), // NEW: dynamic groups
        ];
    }
}
```

### Phase 2: API Integration & Dynamic Payment Groups (Week 2-3)

#### 2.1 PayOp API Client Implementation
**Core Functionality:**
- HTTP client with proper error handling
- Signature generation for invoice creation
- JWT token management for checkout operations
- Response validation and parsing

**API Client Structure (Based on documented endpoints):**
```php
class PayOp_API_Client {
    private $public_key;
    private $secret_key;
    private $jwt_token;

    // From documented endpoint: GET /v1/instrument-settings/payment-methods/available-for-application/{ID}
    public function get_payment_methods(int $application_id): array

    // From documented endpoint: POST /v1/invoices/create
    public function create_invoice(array $order_data): string

    // From documented endpoint: POST /v1/checkout/create
    public function create_checkout(string $invoice_id, array $customer_data): array

    // From documented endpoint: GET /v1/checkout/check-invoice-status/{invoiceID}
    public function check_invoice_status(string $invoice_id): array

    // From documented endpoint: GET /v1/invoices/{invoiceID}
    public function get_invoice_info(string $invoice_id): array

    // From documented endpoint: GET /v2/transactions/{transactionID}
    public function get_transaction_details(string $transaction_id): array

    // From documented endpoint: POST /v1/checkout/void
    public function void_transaction(string $invoice_id): bool
    
    // Cache management for 122 payment methods
    public function get_cached_payment_methods(): ?array
    public function cache_payment_methods(array $methods, int $ttl = 3600): void
}
```

#### 2.2 Signature Generation System
**Implementation based on API documentation:**
```php
class PayOp_Signature_Generator {
    /**
     * Generate PayOp signature using documented formula
     * Formula: SHA256(amount:currency:order_id:secret_key)
     * 
     * @param string $amount Order amount (e.g., "10.00")
     * @param string $currency Currency code (e.g., "EUR")
     * @param string $order_id Unique order identifier
     * @param string $secret_key PayOp secret key
     * @return string SHA-256 signature
     */
    public static function generate(string $amount, string $currency, string $order_id, string $secret_key): string {
        $data = [$amount, $currency, $order_id, $secret_key];
        return hash('sha256', implode(':', $data));
    }
    
    /**
     * Validate signature format
     */
    public static function validate_signature(string $signature): bool {
        return preg_match('/^[a-f0-9]{64}$/', $signature) === 1;
    }
}
```

#### 2.3 Dynamic Payment Groups System
**New Architecture for Configurable Payment Groups:**

**Payment Groups Manager:**
```php
class PayOp_Payment_Groups {
    /**
     * Get all configured payment groups
     */
    public function get_groups(): array

    /**
     * Create new payment group
     */
    public function create_group(array $group_data): int

    /**
     * Update existing payment group
     */
    public function update_group(int $group_id, array $group_data): bool

    /**
     * Delete payment group
     */
    public function delete_group(int $group_id): bool

    /**
     * Assign payment methods to group
     */
    public function assign_methods_to_group(int $group_id, array $method_ids): bool

    /**
     * Get payment methods for specific group
     */
    public function get_group_methods(int $group_id, array $filters = []): array

    /**
     * Get groups available for current cart/customer context
     */
    public function get_available_groups(array $context): array
}
```

#### 2.4 Enhanced Payment Methods Management
**Dynamic Method Loading with Group Support:**
```php
class PayOp_Payment_Methods {
    /**
     * Fetch all 122 payment methods from PayOp API
     */
    public function fetch_methods(): array

    /**
     * Filter methods by supported currencies (EUR, USD, PHP, GBP, CAD, AUD, BRL, DKK)
     */
    public function filter_by_currency(string $currency): array

    /**
     * Filter methods by customer country (226+ supported countries)
     */
    public function filter_by_country(string $country): array

    /**
     * Group methods by admin-configured groups (NEW)
     */
    public function group_by_admin_config(): array

    /**
     * Get methods for specific payment group (NEW)
     */
    public function get_methods_for_group(int $group_id): array

    /**
     * Get required fields configuration for specific payment method
     */
    public function get_method_fields(int $method_id): array

    /**
     * Cache payment methods to reduce API calls
     */
    public function cache_methods(array $methods, int $expiry = 3600): void

    /**
     * Get cached methods if available and not expired
     */
    public function get_cached_methods(): ?array
}
```

### Phase 3: WooCommerce Blocks Integration & Dynamic Fields (Week 3-4)

#### 3.1 WooCommerce Blocks Payment Method Registration
**Block-Based Checkout Integration:**

**Client-Side Registration:**
```javascript
import { registerPaymentMethod } from '@woocommerce/blocks-registry';

const PayOpPaymentMethod = {
    name: 'payop',
    label: <PayOpLabel />,
    content: <PayOpContent />,
    edit: <PayOpEdit />,
    canMakePayment: ({ cart, cartTotals, billingAddress }) => {
        // Dynamic availability based on cart/customer context
        return true;
    },
    supports: {
        features: ['products'],
        showSavedCards: false,
        showSaveOption: false,
    },
};

registerPaymentMethod(PayOpPaymentMethod);
```

**React Components for Dynamic Payment Groups:**
```javascript
const PayOpContent = () => {
    const [selectedGroup, setSelectedGroup] = useState(null);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [additionalFields, setAdditionalFields] = useState([]);

    return (
        <div className="payop-payment-method">
            <PaymentGroupSelector
                groups={paymentGroups}
                onGroupSelect={setSelectedGroup}
            />
            {selectedGroup && (
                <PaymentMethodSelector
                    methods={selectedGroup.methods}
                    onMethodSelect={setSelectedMethod}
                />
            )}
            {selectedMethod && (
                <DynamicFieldRenderer
                    fields={selectedMethod.requiredFields}
                    onFieldChange={handleFieldChange}
                />
            )}
        </div>
    );
};
```

#### 3.2 WooCommerce Additional Checkout Fields Integration
**Using WooCommerce's Native Additional Fields API:**
```php
// Register dynamic fields based on selected payment method
add_action('woocommerce_init', function() {
    $payment_methods = PayOp_Payment_Methods::get_all_methods();

    foreach ($payment_methods as $method) {
        $required_fields = $method['config']['fields'];

        foreach ($required_fields as $field) {
            if (!in_array($field['name'], ['email', 'name'])) { // Skip standard fields
                woocommerce_register_additional_checkout_field([
                    'id' => "payop/{$field['name']}",
                    'label' => $field['title'] ?? ucfirst($field['name']),
                    'location' => 'order',
                    'type' => $field['type'] === 'bank_code' ? 'select' : 'text',
                    'required' => $field['required'] ?? false,
                    'hidden' => [
                        'type' => 'object',
                        'properties' => [
                            'checkout' => [
                                'properties' => [
                                    'payment_method' => [
                                        'not' => ['const' => 'payop']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'validation' => $this->get_field_validation_schema($field),
                ]);
            }
        }
    }
});
```

#### 3.3 Enhanced Field Configuration System
**Based on API analysis of field requirements:**
- Email field: Required by all 122 methods
- Name field: Required by 120 methods
- Phone field: Required by 48 methods
- Document field: Required by 37 methods (with regional patterns)
- Banking fields: Required by 14 methods

**Field Manager Implementation:**
```php
class PayOp_Field_Manager {
    /**
     * Get required fields for specific payment method based on config.fields
     * Common patterns:
     * - All 122 methods: email (required)
     * - 120 methods: name (required) 
     * - 48 methods: phone (required)
     * - 37 methods: document (required, with regional patterns)
     * - 18 methods: date_of_birth (required, European banking)
     * - 14 methods: bank_code + bank_type (required)
     */
    public function get_required_fields(int $method_id): array
    
    /**
     * Validate field data against PayOp patterns
     */
    public function validate_field_data(array $data, array $field_config): bool
    
    /**
     * Apply regional validation patterns
     */
    public function apply_field_patterns(string $value, string $pattern): bool
    
    /**
     * Get field validation rules with error messages
     */
    public function get_field_validation_rules(array $field_config): array
    
    /**
     * Generate field labels with proper localization
     */
    public function get_field_label(string $field_name, array $field_config): string
    
    /**
     * Validate document patterns by region
     */
    public function validate_document_pattern(string $document, string $country): bool
    
    /**
     * Validate bank transfer specific fields
     */
    public function validate_bank_fields(array $bank_data, string $country): bool
}
```

#### 3.2 Regional Field Patterns
**Document Validation Patterns (37 methods require documents):**
- **Colombian/Latin American Documents**: `^\d{6,10}$` (6-10 digits) - 39 methods in Colombia
- **Uruguayan CI Documents**: `^\d{6,8}$` (6-8 digits)
- **Brazilian CPF/CNPJ**: `^\d{11,14}$` (11-14 digits) - 8 methods in Brazil

**Banking Patterns (14 methods require banking details):**
- **SEPA Transfers**: `^(SEPA|SEPA_INSTANT)$` - European countries
- **UK FPS Transfers**: `^(FPS)$` - United Kingdom
- **IBAN Classification**: `^(GB|NOT_GB)$` - IBAN routing

**Country Groupings for Field Requirements:**
- **European SEPA Zone**: Requires date_of_birth + bank_code + bank_type
- **Latin America**: Requires document + phone (high fraud prevention)
- **Asia-Pacific**: Minimal requirements (email + name typically sufficient)
- **North America**: Standard requirements (email + name)

**Field Complexity by Payment Type:**
- **Cards International (1 method)**: Simplest - email + name only
- **E-Wallets (10 methods)**: Simple - email + name only  
- **Cash Payments (42 methods)**: Medium - email + name + document (regional)
- **Bank Transfers (68 methods)**: Complex - email + name + banking details + sometimes date_of_birth
- **Crypto (1 method)**: Simple - email + name only

#### 3.3 Dynamic Form Generation
**Frontend Implementation:**
- JavaScript-based field injection
- Real-time validation
- Progressive enhancement
- Accessibility compliance

### Phase 4: Advanced Block Features & Admin Interface (Week 4-5)

#### 4.1 Advanced Block-Based Features
**Enhanced Payment Method Selection:**
```javascript
const PayOpAdvancedContent = () => {
    const { cart, billingAddress, shippingAddress } = useSelect(select => ({
        cart: select('wc/store/cart').getCartData(),
        billingAddress: select('wc/store/cart').getBillingAddress(),
        shippingAddress: select('wc/store/cart').getShippingAddress(),
    }));

    const [paymentGroups, setPaymentGroups] = useState([]);
    const [filteredMethods, setFilteredMethods] = useState([]);

    useEffect(() => {
        // Filter payment groups based on cart context
        const availableGroups = filterGroupsByContext({
            currency: cart.totals.currency_code,
            country: billingAddress.country,
            cartTotal: cart.totals.total_price,
            items: cart.items,
        });
        setPaymentGroups(availableGroups);
    }, [cart, billingAddress]);

    return (
        <div className="payop-advanced-payment">
            <PaymentGroupTabs groups={paymentGroups} />
            <PaymentMethodGrid methods={filteredMethods} />
            <ConditionalFieldRenderer />
        </div>
    );
};
```

#### 4.2 Dynamic Payment Group Admin Interface
**Admin Interface for Group Management:**
```php
class PayOp_Group_Manager {
    public function render_groups_page() {
        // React-based admin interface for managing payment groups
    }

    public function handle_group_ajax_requests() {
        // AJAX handlers for group CRUD operations
    }

    public function get_group_configuration_schema(): array {
        return [
            'name' => ['type' => 'string', 'required' => true],
            'description' => ['type' => 'string'],
            'display_order' => ['type' => 'integer'],
            'conditions' => [
                'currency' => ['type' => 'array'],
                'country' => ['type' => 'array'],
                'min_amount' => ['type' => 'number'],
                'max_amount' => ['type' => 'number'],
            ],
            'methods' => ['type' => 'array'],
        ];
    }
}
```

#### 4.3 Direct Integration Payment Flow Implementation
**Based on PayOp Direct Integration (NOT using PayOp hosted checkout):**

**Step-by-Step Flow:**
1. **Initial Email Collection**: Collect email address only to minimize checkout friction
2. **Payment Method Fetching**: Call `GET /v1/instrument-settings/payment-methods/available-for-application/{ID}` to get 122 available methods
3. **Method Filtering**: Filter by store currency and customer country from 226+ supported countries
4. **Payment Method Selection**: Display filtered methods grouped by type or region
5. **Dynamic Field Rendering**: Based on selected method's `config.fields`, render additional required fields:
   - **Simple Methods**: Email + name only (cards_international, crypto, most e-wallets)
   - **Medium Complexity**: Email + name + phone (some cash payments)
   - **Complex Methods**: Email + name + phone + document + banking details (Latin American PSE, European SEPA)
6. **Real-time Validation**: Apply field patterns and validation rules as user types
7. **Order Placement**: User clicks "Place Order" with all required data collected
8. **Invoice Creation**: Call `POST /v1/invoices/create` with proper SHA-256 signature
9. **Checkout Transaction**: Call `POST /v1/checkout/create` with collected customer data and selected payment method
10. **Status Polling**: Use `GET /v1/checkout/check-invoice-status/{invoiceID}` to get redirect information
11. **Direct Redirect**: Extract `form.url` from status response and redirect customer directly to payment provider
12. **IPN Processing**: Handle payment notifications from PayOp's documented IP addresses

**Critical Implementation Note**: The plugin completely bypasses PayOp's hosted checkout page. The `check-invoice-status` endpoint returns the payment provider's URL for direct redirection.

### Phase 5: Enhanced Admin Interface & Group Management (Week 5-6)

#### 5.1 Comprehensive Settings Panel
**Enhanced Configuration Options:**
- API credentials management (Public Key, Secret Key, JWT Token)
- **Dynamic payment group configuration**
- **Block vs. legacy checkout preferences**
- Payment method enable/disable controls
- **Advanced grouping and display options**
- **Conditional display rules**
- Logging and debugging settings
- Invoice expiration configuration (default 24 hours)

#### 5.2 Advanced Payment Method & Group Manager
**Enhanced Admin Features:**
- **Interactive payment group builder with drag-and-drop**
- **Visual payment method grid with group assignment**
- **Real-time preview of customer-facing display**
- Bulk enable/disable operations
- **Advanced filtering (country, currency, cart conditions)**
- **Group-based method organization**
- **Field requirement preview by payment method**
- **Performance analytics per group/method**

**Group Management Interface:**
```php
class PayOp_Admin_Group_Interface {
    public function render_group_builder() {
        // React-based group builder interface
        wp_enqueue_script('payop-group-builder');
        echo '<div id="payop-group-builder"></div>';
    }

    public function get_group_builder_data(): array {
        return [
            'availableMethods' => $this->get_all_payment_methods(),
            'existingGroups' => $this->get_configured_groups(),
            'currencies' => $this->get_supported_currencies(),
            'countries' => $this->get_supported_countries(),
        ];
    }
}
```

#### 5.3 Order Management Integration
**HPOS Compatibility:**
- Custom order meta handling for PayOp transaction data
- Transaction status tracking with invoice and transaction IDs
- Order notes integration for payment status updates
- Support for WooCommerce order status mapping:
  - PayOp Invoice Status 0 (New) → WooCommerce Pending
  - PayOp Invoice Status 1 (Paid) → WooCommerce Processing/Completed  
  - PayOp Invoice Status 2 (Overdue) → WooCommerce Cancelled
  - PayOp Invoice Status 4 (Pending) → WooCommerce On Hold
  - PayOp Invoice Status 5 (Failed) → WooCommerce Failed

**Note**: Refunds and withdrawals are handled manually outside the plugin - no automated refund processing implemented.

### Phase 6: Security & Error Handling (Week 6-7)

#### 6.1 Security Implementation
**Security Measures:**
- Input sanitization and validation
- SQL injection prevention
- XSS protection
- CSRF token verification
- Secure API communication

#### 6.2 Error Handling System
**Based on PayOp API error codes and documented responses:**
- **401 Unauthorized**: JWT token expired or invalid - implement automatic token refresh logic
- **403 Forbidden**: Permission/access denied - log and display user-friendly message
- **404 Not Found**: Invoice/transaction not found - validate IDs and retry mechanism
- **422 Unprocessable Entity**: Field validation errors or payment method disabled - display specific field errors
- **500 Internal Server Error**: PayOp server issues - implement exponential backoff retry

**Invoice Status Error Handling:**
- **Status 0 (New)**: Invoice created but no payment initiated
- **Status 1 (Paid)**: Payment successful - complete order
- **Status 2 (Overdue)**: Invoice expired after 24 hours - create new invoice
- **Status 4 (Pending)**: Payment in progress - continue polling
- **Status 5 (Failed)**: Payment failed - display error and allow retry

**Transaction State Error Handling:**
- **State 1 (New)**: No action taken yet
- **State 2 (Accepted)**: Payment successful 
- **State 3 (Failed)**: Technical/financial failure
- **State 4 (Pending)**: Awaiting payment
- **State 5 (Failed)**: Alternative failure state
- **State 9 (Pre-approved)**: Submitted, awaiting funds
- **State 15 (Timeout)**: Lack of final confirmation

#### 6.3 Logging and Monitoring
**Comprehensive Logging:**
- API request/response logging
- Error tracking and reporting
- Performance monitoring
- User action auditing

### Phase 7: IPN & Webhook Handling (Week 7-8)

#### 7.1 IPN Endpoint Implementation
**Webhook Processing:**
```php
class PayOp_IPN_Handler {
    public function handle_ipn_request()
    public function validate_ipn_source(string $ip): bool
    public function process_payment_notification(array $data): bool
    public function update_order_status(int $order_id, string $status): void
}
```

#### 7.2 IP Whitelisting and Security
**Security Implementation:**
- **PayOp IP Validation**: Verify IPN requests come from documented IP addresses:
  - *************
  - *************  
  - ************
  - *************
- **Request signature verification**: Validate IPN payload integrity
- **Duplicate notification handling**: Store processed IPN IDs to prevent duplicate processing
- **Status change processing**: Only update order status if new status differs from current

**IPN Payload Structure (documented format):**
```php
{
  "invoice": {
    "id": "invoice_uuid",
    "status": 1, // Invoice status code
    "txid": "transaction_uuid",
    "metadata": {
      "orderId": "wc_order_id",
      "amount": 10.00,
      "customerId": 12345
    }
  },
  "transaction": {
    "id": "transaction_uuid", 
    "state": 2, // Transaction state code
    "order": {
      "id": "order_identifier"
    },
    "error": {
      "message": "Error description",
      "code": "error_code"
    }
  }
}
```

### Phase 8: Testing & Quality Assurance (Week 8-9)

#### 8.1 Unit Testing
**Test Coverage:**
- API client functionality
- Signature generation
- Field validation
- Payment method filtering
- Error handling scenarios

#### 8.2 Integration Testing
**End-to-End Testing Scenarios:**
- **Payment Method Coverage**: Test representative methods from each type:
  - Cards International (ID: 700001) - Global coverage, simple fields
  - European SEPA (ID: 200031) - Complex banking fields with date_of_birth
  - Colombian PSE (ID: 634) - Document validation with regex patterns  
  - Philippines E-Wallet (ID: 862) - Regional e-wallet, simple fields
  - Cash Payment (ID: 705) - Document + phone requirements
  - Crypto Payment (ID: 6110) - Global crypto, simple fields
- **Currency Testing**: Test all 8 supported currencies (EUR, USD, PHP, GBP, CAD, AUD, BRL, DKK)
- **Geographic Testing**: Test major country markets:
  - Europe: Germany, France, Austria (SEPA methods)
  - Latin America: Colombia, Peru, Mexico (document-heavy methods)  
  - Asia-Pacific: Philippines (e-wallet methods)
  - North America: USA, Canada (card methods)
- **Field Validation Testing**: Test all documented regex patterns and validation rules
- **Error Scenarios**: Test all HTTP error codes and transaction states
- **IPN Processing**: Test with actual PayOp IPN payloads from documented IP addresses

**Performance Testing Targets:**
- Payment method API call: < 2 seconds (122 methods, ~54KB response)
- Invoice creation: < 1 second
- Checkout transaction: < 2 seconds  
- Status polling: < 1 second per poll

#### 8.3 Performance Testing
**Optimization Areas:**
- API response caching
- Database query optimization
- Frontend asset loading
- Memory usage monitoring

### Phase 9: Documentation & Deployment (Week 9-10)

#### 9.1 Technical Documentation
**Documentation Deliverables:**
- Installation guide with PayOp account setup requirements
- Configuration manual for all 122 payment methods
- API integration details with signature generation examples
- Field validation patterns and regional requirements
- Troubleshooting guide for common error scenarios
- Developer documentation with code examples

#### 9.2 User Documentation
**End-User Guides:**
- Admin setup instructions with PayOp credential configuration
- Payment method configuration and grouping strategies
- Order management procedures with HPOS support
- Customer support guidelines for payment issues
- Regional compliance considerations

#### 9.3 Deployment Preparation
**Release Checklist:**
- Code review and optimization for 122 payment method support
- Security audit focusing on signature generation and IPN handling
- Performance validation with large payment method datasets
- WordPress.org compliance check
- Version control and tagging

**Production Deployment Considerations:**
- PayOp has no sandbox environment - all testing occurs on production API
- Implement feature flags for gradual payment method rollout
- Monitor API rate limits and caching effectiveness
- Set up comprehensive logging for production troubleshooting

## Technical Specifications

### Enhanced Database Schema
**Custom Tables with Dynamic Group Support:**
```sql
-- Payment method cache with detailed metadata
CREATE TABLE {prefix}_payop_payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_id INT NOT NULL,
    method_type ENUM('bank_transfer', 'cash', 'ewallet', 'cards_international', 'crypto') NOT NULL,
    method_title VARCHAR(255) NOT NULL,
    method_logo_url TEXT,
    currencies JSON NOT NULL,
    countries JSON NOT NULL,
    config_fields JSON NOT NULL,
    is_enabled TINYINT(1) DEFAULT 1,
    last_updated DATETIME NOT NULL,
    cache_expires DATETIME NOT NULL,
    INDEX idx_method_id (method_id),
    INDEX idx_method_type (method_type),
    INDEX idx_enabled (is_enabled),
    INDEX idx_cache_expires (cache_expires)
);

-- NEW: Payment groups configuration
CREATE TABLE {prefix}_payop_payment_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    display_order INT DEFAULT 0,
    is_enabled TINYINT(1) DEFAULT 1,
    conditions JSON, -- Currency, country, amount conditions
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    INDEX idx_enabled (is_enabled),
    INDEX idx_display_order (display_order)
);

-- NEW: Payment method to group assignments
CREATE TABLE {prefix}_payop_group_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    method_id INT NOT NULL,
    display_order INT DEFAULT 0,
    is_enabled TINYINT(1) DEFAULT 1,
    FOREIGN KEY (group_id) REFERENCES {prefix}_payop_payment_groups(id) ON DELETE CASCADE,
    INDEX idx_group_id (group_id),
    INDEX idx_method_id (method_id),
    INDEX idx_enabled (is_enabled),
    UNIQUE KEY unique_group_method (group_id, method_id)
);

-- Enhanced transaction logs with PayOp-specific data
CREATE TABLE {prefix}_payop_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    invoice_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255),
    payment_method_id INT NOT NULL,
    payment_method_type VARCHAR(50) NOT NULL,
    invoice_status TINYINT NOT NULL DEFAULT 0,
    transaction_state TINYINT NOT NULL DEFAULT 1,
    customer_data JSON,
    payop_response JSON,
    redirect_url TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    INDEX idx_order_id (order_id),
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_invoice_status (invoice_status),
    INDEX idx_transaction_state (transaction_state),
    INDEX idx_expires_at (expires_at)
);

-- IPN processing log for debugging and compliance
CREATE TABLE {prefix}_payop_ipn_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255),
    source_ip VARCHAR(45) NOT NULL,
    payload JSON NOT NULL,
    processed_at DATETIME NOT NULL,
    processing_result ENUM('success', 'duplicate', 'invalid_ip', 'invalid_data', 'error') NOT NULL,
    error_message TEXT,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_source_ip (source_ip),
    INDEX idx_processed_at (processed_at),
    INDEX idx_result (processing_result)
);
```

### Enhanced Configuration Constants
**Based on factual API documentation analysis with block-based checkout support:**

```php
// API Configuration - From documented endpoints
// These will be configurable options, not hardcoded constants
class PayOp_Constants {
    const API_BASE_URL = 'https://api.payop.com';
    const API_VERSION_V1 = '/v1';
    const API_VERSION_V2 = '/v2';

    // Documented API Endpoints
    const ENDPOINT_PAYMENT_METHODS = '/v1/instrument-settings/payment-methods/available-for-application';
    const ENDPOINT_INVOICE_CREATE = '/v1/invoices/create';
    const ENDPOINT_INVOICE_GET = '/v1/invoices';
    const ENDPOINT_CHECKOUT_CREATE = '/v1/checkout/create';
    const ENDPOINT_CHECKOUT_STATUS = '/v1/checkout/check-invoice-status';
    const ENDPOINT_TRANSACTION_GET = '/v2/transactions';
    const ENDPOINT_CHECKOUT_VOID = '/v1/checkout/void';

    // IPN Security - From documentation
    const IPN_ALLOWED_IPS = [
        '*************',
        '*************',
        '************',
        '*************'
    ];

    // Invoice Status Codes
    const INVOICE_STATUS_NEW = 0;
    const INVOICE_STATUS_PAID = 1;
    const INVOICE_STATUS_OVERDUE = 2;
    const INVOICE_STATUS_PENDING = 4;
    const INVOICE_STATUS_FAILED = 5;

    // Transaction State Codes
    const TRANSACTION_STATE_NEW = 1;
    const TRANSACTION_STATE_ACCEPTED = 2;
    const TRANSACTION_STATE_FAILED = 3;
    const TRANSACTION_STATE_PENDING = 4;
    const TRANSACTION_STATE_TIMEOUT = 15;

    // NEW: Block-based checkout constants
    const BLOCK_PAYMENT_METHOD_NAME = 'payop';
    const BLOCK_SCRIPT_HANDLE = 'payop-blocks-integration';
    const BLOCK_STYLE_HANDLE = 'payop-blocks-style';

    // NEW: Payment group constants
    const DEFAULT_GROUP_CARDS = 'cards';
    const DEFAULT_GROUP_BANKING = 'banking';
    const DEFAULT_GROUP_WALLETS = 'wallets';
    const DEFAULT_GROUP_CASH = 'cash';
    const DEFAULT_GROUP_CRYPTO = 'crypto';
}
```

## Risk Mitigation

### Technical Risks
1. **API Rate Limiting**: Implement caching for 122 payment methods with 1-hour TTL to reduce API calls
2. **Payment Method Changes**: Regular API sync with fallback to cached methods if API unavailable
3. **Field Validation Complexity**: Comprehensive validation library for 37 different document patterns and banking requirements
4. **Performance Impact**: Lazy loading of payment methods and progressive field rendering
5. **No Sandbox Environment**: PayOp lacks sandbox - requires careful production testing with logging
6. **Block-Based Checkout Compatibility**: Ensure compatibility across different WooCommerce versions and themes
7. **Dynamic Group Complexity**: Performance impact of real-time group filtering and method availability checks
8. **Legacy vs. Block Checkout**: Maintaining feature parity between shortcode and block-based implementations

### Business Risks  
1. **Compliance Requirements**: Regional document validation for 226+ countries with specific patterns
2. **Currency Fluctuations**: Real-time rate handling for 8 supported currencies
3. **Regional Regulations**: Flexible field configuration for varying KYC requirements by country
4. **User Experience**: Extensive testing across 122 payment methods with different field requirements

### PayOp-Specific Implementation Challenges
1. **Dynamic Field Management**: Handle varying field requirements from simple (2 fields) to complex (6+ fields)
2. **Regional Validation**: Implement 37 different document patterns and banking field validations
3. **Status Polling**: Efficient polling strategy for checkout status without overwhelming the API
4. **Direct Integration Complexity**: Parse form data from status API to redirect to correct payment provider
5. **IPN Security**: Validate IPNs from 4 specific IP addresses with proper payload verification
6. **Block-Based Dynamic Fields**: Integrate PayOp's dynamic field requirements with WooCommerce's additional checkout fields API
7. **Real-time Group Filtering**: Efficiently filter 122 payment methods into dynamic groups based on cart/customer context
8. **Cross-Platform Consistency**: Ensure identical functionality between block-based and legacy checkout experiences

## Success Metrics

### Technical KPIs
- API response time < 2 seconds (critical for 122 payment methods loading)
- Payment success rate > 95% across all method types
- Error rate < 1% for signature generation and field validation
- Page load impact < 500ms with lazy-loaded payment methods
- Cache hit rate > 80% for payment methods API calls

### Business KPIs  
- **Payment Method Adoption**: Track usage across 5 payment types
- **Geographic Coverage**: Monitor adoption across 226+ supported countries
- **Currency Distribution**: Track transactions across 8 supported currencies
- **Conversion Rate**: Measure checkout completion by payment method complexity
- **Regional Performance**: Compare simple vs complex field requirement conversion rates
- **Customer Satisfaction**: Monitor support tickets related to payment method confusion
- **Admin Efficiency**: Reduce payment method configuration time

### PayOp Integration Specific Metrics
- **Field Validation Accuracy**: < 0.1% false rejection rate for regional patterns
- **IPN Processing Reliability**: 100% successful processing of valid IPNs
- **Payment Method Cache Effectiveness**: Reduce API calls by 80% through caching
- **Status Polling Efficiency**: Average 2-3 polls per transaction completion
- **Document Validation Success**: > 95% accuracy for regional document patterns
- **Block-Based Checkout Adoption**: Track usage of block vs. legacy checkout
- **Dynamic Group Performance**: < 500ms for group filtering and method loading
- **Admin Interface Efficiency**: Reduce group configuration time by 70%

## Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| 1 | 2 weeks | Foundation & Block-Based Architecture |
| 2 | 1 week | API Integration & Dynamic Payment Groups |
| 3 | 1 week | WooCommerce Blocks Integration & Dynamic Fields |
| 4 | 1 week | Advanced Block Features & Admin Interface |
| 5 | 1 week | Enhanced Admin Interface & Group Management |
| 6 | 1 week | Security & Error Handling |
| 7 | 1 week | IPN & Webhook Handling |
| 8 | 1 week | Testing & QA (Block + Legacy) |
| 9 | 1 week | Documentation & Deployment |

**Total Development Time**: 10 weeks

## Next Steps

1. **Development Environment Setup**: Configure Local by Flywheel development environment
2. **PayOp Project Access**: Obtain PayOp production credentials (Public Key, Secret Key, JWT Token) - Note: PayOp has no sandbox environment
3. **Repository Setup**: Initialize version control and project structure  
4. **Phase 1 Kickoff**: Begin foundation development with core plugin structure
5. **API Integration Testing**: Test with PayOp production API using documented test credentials pattern

**Important Note**: Since PayOp does not provide a sandbox environment, initial development and testing must be conducted carefully with production API endpoints using small transaction amounts or test payment methods where available.

This comprehensive development plan is based on systematic analysis of PayOp's actual API documentation and payment method data. The plan addresses the complexity of integrating 122 payment methods across 226+ countries with varying field requirements, from simple 2-field methods to complex 6+ field banking integrations.

**Key Technical Achievements:**
- **Block-Based Checkout First**: Primary integration with WooCommerce Blocks (default since 8.3)
- **Dynamic Payment Groups**: Admin-configurable grouping system replacing hard-coded groups
- **Dual Compatibility**: Support for both block-based and legacy shortcode checkout
- **Advanced Admin Interface**: Visual group builder with drag-and-drop functionality
- Direct integration bypassing PayOp's hosted checkout completely
- Dynamic field management handling 37 different document validation patterns using WooCommerce's additional checkout fields API
- Comprehensive support for 8 currencies and 5 payment method types
- Production-ready implementation with no sandbox dependency
- WooCommerce Blocks compatibility with HPOS support
- Simplified scope: No refund/withdrawal automation (handled manually)
- One-time payment focus (no subscription complexity)

**Implementation Priorities:**
1. **Phase 1-2 (Weeks 1-3)**: Block-based foundation and dynamic payment groups - Critical for modern WooCommerce compatibility
2. **Phase 3-4 (Weeks 4-5)**: Advanced block features and admin interface - Core user experience and management
3. **Phase 5-7 (Weeks 6-8)**: Enhanced admin interface, security, and IPN - Production readiness
4. **Phase 8-9 (Weeks 9-10)**: Comprehensive testing and documentation - Quality assurance and deployment

The systematic approach ensures robust handling of PayOp's diverse payment ecosystem while prioritizing modern WooCommerce block-based checkout, providing merchants with a scalable, secure, and future-proof payment solution that supports both current and legacy checkout systems.

## Modern WooCommerce Compatibility Summary

This updated development plan addresses the critical shift to WooCommerce block-based checkout (default since November 2023) while maintaining backward compatibility. Key enhancements include:

### Block-Based Checkout Integration
- **Primary Focus**: WooCommerce Blocks integration using `registerPaymentMethod()` and `AbstractPaymentMethodType`
- **React Components**: Modern frontend with dynamic payment group selection
- **Additional Checkout Fields**: Integration with WooCommerce's native additional fields API
- **Real-time Validation**: JSON Schema-based conditional field validation

### Dynamic Payment Groups
- **Admin-Configurable**: Replace hard-coded groups with flexible, admin-managed system
- **Visual Interface**: Drag-and-drop group builder with real-time preview
- **Conditional Display**: Show/hide groups based on currency, country, cart contents
- **Performance Optimized**: Efficient caching and lazy loading

### Dual Compatibility
- **Block-First Design**: Primary development focus on block-based checkout
- **Legacy Support**: Maintained compatibility with shortcode-based checkout
- **Feature Parity**: Consistent functionality across both systems
- **Migration Path**: Clear upgrade path for existing installations

This comprehensive approach ensures the PayOp plugin is ready for the modern WooCommerce ecosystem while providing merchants with powerful, flexible payment management capabilities.
