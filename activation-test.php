<?php
/**
 * PayOp Plugin Activation Test
 * 
 * This file tests if the plugin can be activated without fatal errors
 */

// Simulate WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', '/Users/<USER>/Local Sites/test/app/public/');
}

// Define required constants
if (!defined('PAYOP_VERSION')) {
    define('PAYOP_VERSION', '1.0.0');
}
if (!defined('PAYOP_PLUGIN_FILE')) {
    define('PAYOP_PLUGIN_FILE', __DIR__ . '/payop-woocommerce-blocks.php');
}
if (!defined('PAYOP_PLUGIN_DIR')) {
    define('PAYOP_PLUGIN_DIR', __DIR__ . '/');
}
if (!defined('PAYOP_PLUGIN_URL')) {
    define('PAYOP_PLUGIN_URL', 'http://test.local/wp-content/plugins/payop-direct-payment-woocommerce/');
}

// Mock WordPress functions
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $args = 1) {
        echo "✓ add_action: $hook\n";
        return true;
    }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $args = 1) {
        echo "✓ add_filter: $hook\n";
        return true;
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        echo "✓ register_activation_hook\n";
        return true;
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        echo "✓ register_deactivation_hook\n";
        return true;
    }
}

if (!function_exists('is_admin')) {
    function is_admin() {
        return false;
    }
}

if (!function_exists('version_compare')) {
    function version_compare($version1, $version2, $operator = null) {
        return \version_compare($version1, $version2, $operator);
    }
}

if (!function_exists('get_bloginfo')) {
    function get_bloginfo($show = '') {
        return '6.4.0';
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://test.local/wp-content/plugins/payop-direct-payment-woocommerce/';
    }
}

if (!function_exists('plugin_basename')) {
    function plugin_basename($file) {
        return 'payop-direct-payment-woocommerce/payop-woocommerce-blocks.php';
    }
}

if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return $default;
    }
}

if (!function_exists('add_option')) {
    function add_option($option, $value) {
        return true;
    }
}

if (!function_exists('wp_die')) {
    function wp_die($message) {
        die($message);
    }
}

if (!function_exists('flush_rewrite_rules')) {
    function flush_rewrite_rules() {
        return true;
    }
}

if (!function_exists('wp_clear_scheduled_hook')) {
    function wp_clear_scheduled_hook($hook) {
        return true;
    }
}

if (!function_exists('load_plugin_textdomain')) {
    function load_plugin_textdomain($domain, $deprecated, $plugin_rel_path) {
        return true;
    }
}

// Mock WooCommerce
if (!class_exists('WooCommerce')) {
    class WooCommerce {
        public function __construct() {}
    }
}

if (!defined('WC_VERSION')) {
    define('WC_VERSION', '8.3.0');
}

if (!function_exists('woocommerce_register_additional_checkout_field')) {
    function woocommerce_register_additional_checkout_field($args) {
        return true;
    }
}

echo "PayOp Plugin Activation Test\n";
echo "============================\n\n";

try {
    echo "Loading main plugin file...\n";
    require_once __DIR__ . '/payop-woocommerce-blocks.php';
    echo "✅ Plugin loaded successfully!\n\n";
    
    echo "Testing class loading...\n";
    
    // Test if classes can be instantiated
    if (class_exists('PayOp_WooCommerce_Blocks')) {
        echo "✓ Main plugin class found\n";
    } else {
        echo "❌ Main plugin class not found\n";
    }
    
    echo "\n✅ All tests passed! Plugin should activate without errors.\n";
    
} catch (Error $e) {
    echo "\n❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n❌ Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
}
